!function(t){var e,r=t.System.constructor.prototype,n=r.register;r.register=function(t,r,f){e="string"==typeof t?f:r,n.apply(this,arguments)};var f=r.getRegister;r.getRegister=function(){var t=f.call(this);if(!t||t[1]===e||0===t[1].length)return t;var r=t[1];return t[1]=function(t,e){var n,f=!1,u=r.call(this,(function(e,r){"object"==typeof e&&e&&e.__useDefault?(n=e.default,f=!0):"default"===e?n=r:"__useDefault"===e&&(f=!0),t(e,r)}),e),i=u.execute;return i&&(u.execute=function(){if(i.call(this),f)for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&"default"!==e&&t(e,n[e])}),u},t}}("undefined"!=typeof self?self:global);
//# sourceMappingURL=named-exports.min.js.map
