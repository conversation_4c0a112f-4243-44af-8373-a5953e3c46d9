{"name": "systemjs", "version": "6.15.1", "main": "dist/system-node.cjs", "exports": {".": {"node": "./dist/system-node.cjs", "default": "./dist/system.min.js"}, "./s.js": "./dist/s.min.js", "./package.json": "./package.json", "./dist/*": "./dist/*"}, "description": "Dynamic ES module loader", "repository": {"type": "git", "url": "git://github.com/systemjs/systemjs"}, "author": "<PERSON>", "type": "script", "license": "MIT", "files": ["dist"], "scripts": {"test": "chomp test", "build": "chomp build"}, "devDependencies": {"@jsenv/file-size-impact": "^5.2.0", "@rollup/plugin-json": "^4.0.2", "@rollup/plugin-node-resolve": "^7.1.1", "@rollup/plugin-replace": "^2.4.2", "@vercel/ncc": "^0.34.0", "bluebird": "^3.7.2", "construct-style-sheets-polyfill": "^2.3.5", "kleur": "^4.1.5", "mkdirp": "^1.0.4", "mocha": "^7.1.1", "node-fetch": "^2.6.0", "open": "^8.4.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-terser": "^5.3.0", "source-map-support": "^0.5.16", "symbol-es6": "^0.1.2", "terser": "^5.16.5", "whatwg-fetch": "^3.0.0"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/systemjs"}}