{"version": 3, "sources": ["../../src/client/window.ts", "../../src/client/context.ts"], "sourcesContent": ["import type { MonkeyWindow } from './types/_context';\n\nconst key =\n  `__monkeyWindow-` +\n  (() => {\n    try {\n      return new URL(import.meta.url).origin;\n    } catch {\n      return location.origin;\n    }\n  })();\n\n// @ts-ignore\nexport const monkeyWindow: MonkeyWindow = document[key] ?? window;\n", "// The current code file is automatically generated using the following command\n// pnpm -F vite-plugin-monkey exec tsx ./scripts/transform_context.ts\n// This command is used to avoid the tedious process of manually copying the code\n\nexport {};\n\nimport { monkeyWindow as w } from './window';\n\nexport const unsafeWindow = /* @__PURE__ */ (() => w.unsafeWindow)();\n\nexport const GM = /* @__PURE__ */ (() => w.GM)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_info\n * @see https://violentmonkey.github.io/api/gm/#gm_info\n */\nexport const GM_info = /* @__PURE__ */ (() => w.GM_info)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_log\n * @available tampermonkey\n */\nexport const GM_log = /* @__PURE__ */ (() => w.GM_log)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_getValue\n * @see https://violentmonkey.github.io/api/gm/#gm_getvalue\n */\nexport const GM_getValue = /* @__PURE__ */ (() => w.GM_getValue)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_getValues\n * @see https://violentmonkey.github.io/api/gm/#gm_getvalues\n */\nexport const GM_getValues = /* @__PURE__ */ (() => w.GM_getValues)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_setValue\n * @see https://violentmonkey.github.io/api/gm/#gm_setvalue\n */\nexport const GM_setValue = /* @__PURE__ */ (() => w.GM_setValue)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_setValues\n * @see https://violentmonkey.github.io/api/gm/#gm_setvalues\n */\nexport const GM_setValues = /* @__PURE__ */ (() => w.GM_setValues)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_deleteValue\n * @see https://violentmonkey.github.io/api/gm/#gm_deletevalue\n */\nexport const GM_deleteValue = /* @__PURE__ */ (() => w.GM_deleteValue)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_deleteValues\n * @see https://violentmonkey.github.io/api/gm/#gm_deletevalues\n */\nexport const GM_deleteValues = /* @__PURE__ */ (() => w.GM_deleteValues)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_listValues\n * @see https://violentmonkey.github.io/api/gm/#gm_listvalues\n */\nexport const GM_listValues = /* @__PURE__ */ (() => w.GM_listValues)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_addValueChangeListener\n * @see https://violentmonkey.github.io/api/gm/#gm_addvaluechangelistener\n */\nexport const GM_addValueChangeListener = /* @__PURE__ */ (() =>\n  w.GM_addValueChangeListener)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_removeValueChangeListener\n * @see https://violentmonkey.github.io/api/gm/#gm_removevaluechangelistener\n */\nexport const GM_removeValueChangeListener = /* @__PURE__ */ (() =>\n  w.GM_removeValueChangeListener)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_getResourceText\n * @see https://violentmonkey.github.io/api/gm/#gm_getresourcetext\n */\nexport const GM_getResourceText = /* @__PURE__ */ (() =>\n  w.GM_getResourceText)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_getResourceURL\n * @see https://violentmonkey.github.io/api/gm/#gm_getresourceurl\n */\nexport const GM_getResourceURL = /* @__PURE__ */ (() => w.GM_getResourceURL)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_addElement\n * @see https://violentmonkey.github.io/api/gm/#gm_addelement\n */\nexport const GM_addElement = /* @__PURE__ */ (() => w.GM_addElement)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_addStyle\n * @see https://violentmonkey.github.io/api/gm/#gm_addstyle\n */\nexport const GM_addStyle = /* @__PURE__ */ (() => w.GM_addStyle)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_openInTab\n * @see https://violentmonkey.github.io/api/gm/#gm_openintab\n */\nexport const GM_openInTab = /* @__PURE__ */ (() => w.GM_openInTab)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_getTab\n * @available tampermonkey\n */\nexport const GM_getTab = /* @__PURE__ */ (() => w.GM_getTab)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_saveTab\n * @available tampermonkey\n */\nexport const GM_saveTab = /* @__PURE__ */ (() => w.GM_saveTab)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_getTabs\n * @available tampermonkey\n */\nexport const GM_getTabs = /* @__PURE__ */ (() => w.GM_getTabs)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_registerMenuCommand\n * @see https://violentmonkey.github.io/api/gm/#gm_registermenucommand\n */\nexport const GM_registerMenuCommand = /* @__PURE__ */ (() =>\n  w.GM_registerMenuCommand)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_unregisterMenuCommand\n * @see https://violentmonkey.github.io/api/gm/#gm_unregistermenucommand\n */\nexport const GM_unregisterMenuCommand = /* @__PURE__ */ (() =>\n  w.GM_unregisterMenuCommand)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_notification\n * @see https://violentmonkey.github.io/api/gm/#gm_notification\n */\nexport const GM_notification = /* @__PURE__ */ (() => w.GM_notification)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_setClipboard\n * @see https://violentmonkey.github.io/api/gm/#gm_setclipboard\n */\nexport const GM_setClipboard = /* @__PURE__ */ (() => w.GM_setClipboard)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_xmlhttpRequest\n * @see https://violentmonkey.github.io/api/gm/#gm_xmlhttprequest\n */\nexport const GM_xmlhttpRequest = /* @__PURE__ */ (() => w.GM_xmlhttpRequest)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_download\n * @see https://violentmonkey.github.io/api/gm/#gm_download\n */\nexport const GM_download = /* @__PURE__ */ (() => w.GM_download)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_webRequest\n * @available tampermonkey\n */\nexport const GM_webRequest = /* @__PURE__ */ (() => w.GM_webRequest)();\n\n/**\n * @see https://www.tampermonkey.net/documentation.php#api:GM_cookie.list\n * @see https://www.tampermonkey.net/documentation.php#api:GM_cookie.set\n * @see https://www.tampermonkey.net/documentation.php#api:GM_cookie.delete\n * @available tampermonkey\n */\nexport const GM_cookie = /* @__PURE__ */ (() => w.GM_cookie)();\n"], "mappings": ";AAEA,IAAM,MACJ,qBACC,MAAM;AACL,MAAI;AACF,WAAO,IAAI,IAAI,YAAY,GAAG,EAAE;AAAA,EAClC,QAAQ;AACN,WAAO,SAAS;AAAA,EAClB;AACF,GAAG;AAGE,IAAM,eAA6B,SAAS,GAAG,KAAK;;;ACLpD,IAAM,eAAgC,uBAAM,aAAE,cAAc;AAE5D,IAAM,KAAsB,uBAAM,aAAE,IAAI;AAMxC,IAAM,UAA2B,uBAAM,aAAE,SAAS;AAMlD,IAAM,SAA0B,uBAAM,aAAE,QAAQ;AAMhD,IAAM,cAA+B,uBAAM,aAAE,aAAa;AAM1D,IAAM,eAAgC,uBAAM,aAAE,cAAc;AAM5D,IAAM,cAA+B,uBAAM,aAAE,aAAa;AAM1D,IAAM,eAAgC,uBAAM,aAAE,cAAc;AAM5D,IAAM,iBAAkC,uBAAM,aAAE,gBAAgB;AAMhE,IAAM,kBAAmC,uBAAM,aAAE,iBAAiB;AAMlE,IAAM,gBAAiC,uBAAM,aAAE,eAAe;AAM9D,IAAM,4BAA6C,uBACxD,aAAE,2BAA2B;AAMxB,IAAM,+BAAgD,uBAC3D,aAAE,8BAA8B;AAM3B,IAAM,qBAAsC,uBACjD,aAAE,oBAAoB;AAMjB,IAAM,oBAAqC,uBAAM,aAAE,mBAAmB;AAMtE,IAAM,gBAAiC,uBAAM,aAAE,eAAe;AAM9D,IAAM,cAA+B,uBAAM,aAAE,aAAa;AAM1D,IAAM,eAAgC,uBAAM,aAAE,cAAc;AAM5D,IAAM,YAA6B,uBAAM,aAAE,WAAW;AAMtD,IAAM,aAA8B,uBAAM,aAAE,YAAY;AAMxD,IAAM,aAA8B,uBAAM,aAAE,YAAY;AAMxD,IAAM,yBAA0C,uBACrD,aAAE,wBAAwB;AAMrB,IAAM,2BAA4C,uBACvD,aAAE,0BAA0B;AAMvB,IAAM,kBAAmC,uBAAM,aAAE,iBAAiB;AAMlE,IAAM,kBAAmC,uBAAM,aAAE,iBAAiB;AAMlE,IAAM,oBAAqC,uBAAM,aAAE,mBAAmB;AAMtE,IAAM,cAA+B,uBAAM,aAAE,aAAa;AAM1D,IAAM,gBAAiC,uBAAM,aAAE,eAAe;AAQ9D,IAAM,YAA6B,uBAAM,aAAE,WAAW;", "names": []}