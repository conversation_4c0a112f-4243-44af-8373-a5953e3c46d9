{"name": "postcss-url", "version": "10.1.3", "description": "PostCSS plugin to rebase or inline on url().", "keywords": ["css", "postcss", "postcss-plugin", "url", "rebase", "inline", "base64", "assets"], "author": "<PERSON><PERSON>", "license": "MIT", "repository": "https://github.com/postcss/postcss-url.git", "main": "src/index.js", "files": ["src"], "engines": {"node": ">=10"}, "dependencies": {"make-dir": "~3.1.0", "mime": "~2.5.2", "minimatch": "~3.0.4", "xxhashjs": "~0.2.2"}, "devDependencies": {"chai": "4.1.2", "eslint": "7.12.1", "mocha": "8.2.1", "npmpub": "5.0.0", "postcss": "^8.0.0", "postcss-import": "12.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}, "scripts": {"lint": "eslint --fix .", "tests": "mocha --recursive --require ./test/setup", "test": "npm run lint && npm run tests", "release": "npmpub"}}