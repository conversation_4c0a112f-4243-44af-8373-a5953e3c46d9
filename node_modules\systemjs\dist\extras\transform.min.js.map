{"version": 3, "file": "##.min.js", "names": ["global", "systemJSPrototype", "System", "constructor", "prototype", "instantiate", "url", "parent", "meta", "slice", "call", "this", "loader", "fetch", "credentials", "then", "res", "ok", "Error", "status", "statusText", "text", "source", "transform", "eval", "getRegister", "_id", "self"], "sources": ["transform.js"], "sourcesContent": ["(function () {\n\n  function errMsg(errCode, msg) {\r\n    return (msg || \"\") + \" (SystemJS Error#\" + errCode + \" \" + \"https://github.com/systemjs/systemjs/blob/main/docs/errors.md#\" + errCode + \")\";\r\n  }\n\n  /*\r\n   * Support for a \"transform\" loader interface\r\n   *\r\n   * Note: This extra is deprecated and will be removed in the next major.\r\n   */\r\n  (function (global) {\r\n    var systemJSPrototype = global.System.constructor.prototype;\r\n\r\n    var instantiate = systemJSPrototype.instantiate;\r\n    systemJSPrototype.instantiate = function (url, parent, meta) {\r\n      if (url.slice(-5) === '.wasm')\r\n        return instantiate.call(this, url, parent, meta);\r\n\r\n      var loader = this;\r\n      return fetch(url, { credentials: 'same-origin' })\r\n      .then(function (res) {\r\n        if (!res.ok)\r\n          throw Error(errMsg(7, 'Fetch error: ' + res.status + ' ' + res.statusText + (parent ? ' loading from ' + parent : '')));\r\n        return res.text();\r\n      })\r\n      .then(function (source) {\r\n        return loader.transform.call(this, url, source);\r\n      })\r\n      .then(function (source) {\r\n        (0, eval)(source + '\\n//# sourceURL=' + url);\r\n        return loader.getRegister(url);\r\n      });\r\n    };\r\n\r\n    // Hookable transform function!\r\n    systemJSPrototype.transform = function (_id, source) {\r\n      return source;\r\n    };\r\n  })(typeof self !== 'undefined' ? self : global);\n\n})();\n"], "mappings": "CAWE,SAAWA,GACT,IAAIC,EAAoBD,EAAOE,OAAOC,YAAYC,UAE9CC,EAAcJ,EAAkBI,YACpCJ,EAAkBI,YAAc,SAAUC,EAAKC,EAAQC,GACrD,GAAsB,UAAlBF,EAAIG,OAAO,GACb,OAAOJ,EAAYK,KAAKC,KAAML,EAAKC,EAAQC,GAE7C,IAAII,EAASD,KACb,OAAOE,MAAMP,EAAK,CAAEQ,YAAa,gBAChCC,MAAK,SAAUC,GACd,IAAKA,EAAIC,GACP,MAAMC,OAAgB,gBAAkBF,EAAIG,OAAS,IAAMH,EAAII,YAAcb,EAAS,iBAAmBA,EAAS,KApBzG,IAAM,oBAoBI,EApBlB,qEAqBH,OAAOS,EAAIK,MACb,IACCN,MAAK,SAAUO,GACd,OAAOV,EAAOW,UAAUb,KAAKC,KAAML,EAAKgB,EAC1C,IACCP,MAAK,SAAUO,GAEd,OADA,EAAIE,MAAMF,EAAS,mBAAqBhB,GACjCM,EAAOa,YAAYnB,EAC5B,GACF,EAGAL,EAAkBsB,UAAY,SAAUG,EAAKJ,GAC3C,OAAOA,CACT,CACD,CA5BD,CA4BmB,oBAATK,KAAuBA,KAAO3B"}