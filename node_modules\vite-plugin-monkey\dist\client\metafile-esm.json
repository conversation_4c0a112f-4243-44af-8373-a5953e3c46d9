{"inputs": {"src/client/window.ts": {"bytes": 296, "imports": [], "format": "esm"}, "src/client/context.ts": {"bytes": 6336, "imports": [{"path": "src/client/window.ts", "kind": "import-statement", "original": "./window"}], "format": "esm"}, "src/client/index.ts": {"bytes": 98, "imports": [{"path": "src/client/window.ts", "kind": "import-statement", "original": "./window"}, {"path": "src/client/context.ts", "kind": "import-statement", "original": "./context"}], "format": "esm"}}, "outputs": {"dist/client/index.mjs.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 8139}, "dist/client/index.mjs": {"imports": [], "exports": ["GM", "GM_addElement", "GM_addStyle", "GM_addValueChangeListener", "GM_cookie", "GM_deleteValue", "GM_deleteValues", "GM_download", "GM_getResourceText", "GM_getResourceURL", "GM_getTab", "GM_getTabs", "GM_getValue", "GM_getValues", "GM_info", "GM_listValues", "GM_log", "GM_notification", "GM_openInTab", "GM_registerMenuCommand", "GM_removeValueChangeListener", "GM_saveTab", "GM_setClipboard", "GM_setValue", "GM_setValues", "GM_unregisterMenuCommand", "GM_webRequest", "GM_xmlhttpRequest", "<PERSON><PERSON><PERSON><PERSON>", "unsafeWindow"], "entryPoint": "src/client/index.ts", "inputs": {"src/client/window.ts": {"bytesInOutput": 185}, "src/client/index.ts": {"bytesInOutput": 0}, "src/client/context.ts": {"bytesInOutput": 2180}}, "bytes": 2952}}}