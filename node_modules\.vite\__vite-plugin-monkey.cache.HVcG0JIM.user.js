// ==UserScript==
// @name       server:TM-Vite-JS-Template
// @namespace  https://example.com
// @version    0.1.0
// @icon       https://raw.githubusercontent.com/tampermonkey/tampermonkey/master/logo/icon48.png
// @match      *://*/*
// @grant      GM.addElement
// @grant      GM.addStyle
// @grant      GM.addValueChangeListener
// @grant      GM.cookie
// @grant      GM.deleteValue
// @grant      GM.deleteValues
// @grant      GM.download
// @grant      GM.getResourceText
// @grant      GM.getResourceUrl
// @grant      GM.getTab
// @grant      GM.getTabs
// @grant      GM.getValue
// @grant      GM.getValues
// @grant      GM.info
// @grant      GM.listValues
// @grant      GM.log
// @grant      GM.notification
// @grant      GM.openInTab
// @grant      GM.registerMenuCommand
// @grant      GM.removeValueChangeListener
// @grant      GM.saveTab
// @grant      GM.setClipboard
// @grant      GM.setValue
// @grant      GM.setValues
// @grant      GM.unregisterMenuCommand
// @grant      GM.webRequest
// @grant      GM.xmlHttpRequest
// @grant      GM_addElement
// @grant      GM_addStyle
// @grant      GM_addValueChangeListener
// @grant      GM_cookie
// @grant      GM_deleteValue
// @grant      GM_deleteValues
// @grant      GM_download
// @grant      GM_getResourceText
// @grant      GM_getResourceURL
// @grant      GM_getTab
// @grant      GM_getTabs
// @grant      GM_getValue
// @grant      GM_getValues
// @grant      GM_info
// @grant      GM_listValues
// @grant      GM_log
// @grant      GM_notification
// @grant      GM_openInTab
// @grant      GM_registerMenuCommand
// @grant      GM_removeValueChangeListener
// @grant      GM_saveTab
// @grant      GM_setClipboard
// @grant      GM_setValue
// @grant      GM_setValues
// @grant      GM_unregisterMenuCommand
// @grant      GM_webRequest
// @grant      GM_xmlhttpRequest
// @grant      unsafeWindow
// @grant      window.close
// @grant      window.focus
// @grant      window.onurlchange
// ==/UserScript==