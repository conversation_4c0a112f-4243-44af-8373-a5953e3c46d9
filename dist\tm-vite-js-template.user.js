// ==UserScript==
// @name       TM-Vite-JS-Template
// @namespace  https://example.com
// @version    0.1.0
// @icon       https://raw.githubusercontent.com/tampermonkey/tampermonkey/master/logo/icon48.png
// @match      *://*/*
// @grant      GM_addStyle
// @grant      GM_getValue
// @grant      GM_registerMenuCommand
// @grant      GM_setValue
// ==/UserScript==

(t=>{if(typeof GM_addStyle=="function"){GM_addStyle(t);return}const e=document.createElement("style");e.textContent=t,document.head.append(e)})(' #tm-template-menu{position:fixed;top:20px;right:20px;z-index:1000000;font-family:system-ui,sans-serif}.tm-menu__button{width:40px;height:40px;background:linear-gradient(135deg,#4f8ef7,#3d7ae0);border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;box-shadow:0 4px 16px #4f8ef766;transition:all .3s ease;border:2px solid rgba(255,255,255,.9);color:#fff}.tm-menu__button:hover{transform:scale(1.1);box-shadow:0 6px 20px #4f8ef799}.tm-menu__button:active{transform:scale(.95)}#tm-template-panel{position:fixed;top:70px;right:20px;z-index:999999;width:420px;background:#fff;box-shadow:0 2px 12px #0003;border-radius:8px;font-size:14px;font-family:system-ui,sans-serif;color:#333}.tm-panel__header{background:#4f8ef7;color:#fff;padding:8px 12px;cursor:move;border-radius:8px 8px 0 0;display:flex;align-items:center;justify-content:space-between;-webkit-user-select:none;user-select:none}.tm-panel__title{font-weight:700;font-size:16px}.tm-panel__actions button{background:#fff3;border:none;color:#fff;padding:4px 8px;margin-left:6px;border-radius:4px;cursor:pointer;font-size:14px}.tm-panel__actions button:hover{background:#ffffff4d}.tm-panel__body{padding:12px}.tm-panel__content{display:flex;gap:16px;min-height:200px}.tm-panel__left{flex:0 0 160px;border-right:1px solid #eee;padding-right:16px}.tm-panel__right{flex:1}.tm-control-section h4,.tm-modules-section h4{margin:0 0 12px;font-size:14px;font-weight:600;color:#333}.tm-switch-container{display:flex;align-items:center;gap:8px;margin-bottom:12px}.tm-switch-label{font-size:13px;color:#666}.tm-switch{position:relative;display:inline-block;width:44px;height:24px}.tm-switch input{opacity:0;width:0;height:0}.tm-slider{position:absolute;cursor:pointer;top:0;left:0;right:0;bottom:0;background-color:#ccc;transition:.4s;border-radius:24px}.tm-slider:before{position:absolute;content:"";height:18px;width:18px;left:3px;bottom:3px;background-color:#fff;transition:.4s;border-radius:50%}.tm-switch input:checked+.tm-slider{background-color:#4f8ef7}.tm-switch input:checked+.tm-slider:before{transform:translate(20px)}.tm-switch input:disabled+.tm-slider{opacity:.5;cursor:not-allowed}.tm-switch-small{width:36px;height:20px}.tm-switch-small .tm-slider:before{height:14px;width:14px;left:3px;bottom:3px}.tm-switch-small input:checked+.tm-slider:before{transform:translate(16px)}.tm-btn{border:none;border-radius:4px;cursor:pointer;font-size:13px;padding:6px 12px;transition:all .2s}.tm-btn-primary{background:#4f8ef7;color:#fff;width:100%}.tm-btn-primary:hover:not(:disabled){background:#3d7ae0}.tm-btn-small{padding:4px 8px;font-size:12px;background:#f5f5f5;color:#666}.tm-btn-small:hover:not(:disabled){background:#e8e8e8}.tm-btn:disabled{opacity:.5;cursor:not-allowed}.tm-module-item{display:flex;align-items:center;justify-content:space-between;padding:8px 0;border-bottom:1px solid #f0f0f0}.tm-module-item:last-child{border-bottom:none}.tm-module-item.disabled{opacity:.5}.tm-module-info{flex:1}.tm-module-name{font-size:13px;font-weight:500;color:#333;margin-bottom:2px}.tm-module-file{font-size:11px;color:#888}.tm-module-controls{display:flex;align-items:center;gap:8px}.tm-no-modules{text-align:center;color:#999;font-size:12px;padding:20px 0}.tm-panel__footer{margin-top:12px;padding-top:8px;border-top:1px solid #eee;font-size:12px;color:#888;text-align:center} ');

(function () {
  'use strict';

  function testHello({ storage: storage2 }) {
    const name = "testHello";
    return {
      name,
      run() {
        const count = storage2.get("hello_count", 0) + 1;
        storage2.set("hello_count", count);
        alert(`Hello from Tampermonkey!
这是第 ${count} 次运行。
当前页面：${location.href}`);
        const btn = document.createElement("button");
        btn.textContent = "Hello按钮";
        Object.assign(btn.style, {
          position: "fixed",
          bottom: "80px",
          right: "20px",
          zIndex: 99999,
          padding: "6px 12px",
          border: "none",
          borderRadius: "6px",
          background: "#4f8ef7",
          color: "#fff",
          cursor: "pointer"
        });
        btn.onclick = () => alert("按钮被点击了！");
        document.body.appendChild(btn);
        console.log(`[${name}] 已运行，count=${count}`);
      }
    };
  }
  const __vite_glob_0_0 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
    __proto__: null,
    default: testHello
  }, Symbol.toStringTag, { value: "Module" }));
  const moduleList = [];
  let storage = null;
  async function registerModules(ctx) {
    storage = ctx.storage;
    moduleList.length = 0;
    const modules = /* @__PURE__ */ Object.assign({ "./testHello.js": __vite_glob_0_0 });
    for (const [path, moduleExport] of Object.entries(modules)) {
      if (path.includes("index.js")) continue;
      try {
        const moduleInstance = moduleExport.default(ctx);
        if (moduleInstance && moduleInstance.name) {
          const isEnabled = storage.get(`module_${moduleInstance.name}_enabled`, true);
          moduleInstance.enabled = isEnabled;
          moduleInstance.filePath = path;
          moduleList.push(moduleInstance);
        }
      } catch (error) {
        console.error(`[模块加载失败] ${path}:`, error);
      }
    }
    console.log(`[模块系统] 已加载 ${moduleList.length} 个模块`);
  }
  function runAllModules() {
    const enabledModules = moduleList.filter((m) => m.enabled);
    console.log(`[模块系统] 运行 ${enabledModules.length} 个启用的模块`);
    enabledModules.forEach((m) => {
      try {
        m.run && m.run();
      } catch (error) {
        console.error(`[模块运行失败] ${m.name}:`, error);
      }
    });
  }
  function getAllModules() {
    return moduleList;
  }
  function toggleModule(moduleName, enabled) {
    const module = moduleList.find((m) => m.name === moduleName);
    if (module) {
      module.enabled = enabled;
      storage.set(`module_${moduleName}_enabled`, enabled);
      console.log(`[模块系统] ${moduleName} ${enabled ? "已启用" : "已禁用"}`);
    }
  }
  function runModule(moduleName) {
    const module = moduleList.find((m) => m.name === moduleName && m.enabled);
    if (module) {
      try {
        module.run && module.run();
        console.log(`[模块系统] 已运行模块: ${moduleName}`);
      } catch (error) {
        console.error(`[模块运行失败] ${moduleName}:`, error);
      }
    }
  }
  function initPanel({ storage: storage2 }) {
    registerTampermonkeyMenu(storage2);
    createMainPanel(storage2);
  }
  function registerTampermonkeyMenu(storage2) {
    if (typeof GM_registerMenuCommand === "undefined") {
      console.warn("[TM-Vite-JS-Template] GM_registerMenuCommand 不可用，将创建页面按钮作为备选方案");
      createFallbackButton(storage2);
      return;
    }
    const isVisible = storage2.get("panel_visible", false);
    GM_registerMenuCommand(
      isVisible ? "隐藏管理面板" : "显示管理面板",
      () => {
        toggleMainPanel(storage2);
        updateMenuCommand(storage2);
      },
      "p"
      // 快捷键
    );
  }
  function updateMenuCommand(storage2) {
    const isVisible = storage2.get("panel_visible", false);
    console.log(`[TM-Vite-JS-Template] 面板${isVisible ? "已显示" : "已隐藏"}，请刷新页面更新菜单文本`);
  }
  function createFallbackButton(storage2) {
    const menuId = "tm-template-menu";
    if (document.getElementById(menuId)) return;
    const menuButton = document.createElement("div");
    menuButton.id = menuId;
    menuButton.innerHTML = `
    <div class="tm-menu__button" title="TM-Vite-JS-Template 管理面板">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
    </div>
  `;
    document.body.appendChild(menuButton);
    const button = menuButton.querySelector(".tm-menu__button");
    button.addEventListener("click", () => {
      toggleMainPanel(storage2);
    });
  }
  function createMainPanel(storage2) {
    const panelId = "tm-template-panel";
    if (document.getElementById(panelId)) return;
    const panel = document.createElement("div");
    panel.id = panelId;
    panel.innerHTML = `
    <div class="tm-panel__header">
      <span class="tm-panel__title">TM-Vite-JS-Template</span>
      <div class="tm-panel__actions">
        <button id="tm-close-panel" title="关闭面板">×</button>
        <button id="tm-toggle">—</button>
      </div>
    </div>
    <div class="tm-panel__body">
      <div class="tm-panel__content">
        <div class="tm-panel__left">
          <div class="tm-control-section">
            <h4>脚本控制</h4>
            <div class="tm-switch-container">
              <label class="tm-switch">
                <input type="checkbox" id="tm-master-switch" checked>
                <span class="tm-slider"></span>
              </label>
              <span class="tm-switch-label">脚本框架</span>
            </div>
            <button id="tm-run-all" class="tm-btn tm-btn-primary">运行所有模块</button>
          </div>
        </div>
        <div class="tm-panel__right">
          <div class="tm-modules-section">
            <h4>模块列表</h4>
            <div id="tm-modules-list"></div>
          </div>
        </div>
      </div>
      <div class="tm-panel__footer">拖拽移动 | 双击标题栏吸附边缘</div>
    </div>
  `;
    document.body.appendChild(panel);
    const isVisible = storage2.get("panel_visible", false);
    panel.style.display = isVisible ? "block" : "none";
    initPanelEvents(panel, storage2);
    updateModulesList(panel);
    dragElement(panel);
  }
  function toggleMainPanel(storage2) {
    const panel = document.getElementById("tm-template-panel");
    if (!panel) return;
    const isVisible = panel.style.display !== "none";
    const newVisible = !isVisible;
    panel.style.display = newVisible ? "block" : "none";
    storage2.set("panel_visible", newVisible);
    console.log(`[TM-Vite-JS-Template] 面板${newVisible ? "已显示" : "已隐藏"}`);
  }
  function initPanelEvents(panel, storage2) {
    const closeBtn = panel.querySelector("#tm-close-panel");
    closeBtn.addEventListener("click", () => {
      panel.style.display = "none";
      storage2.set("panel_visible", false);
      console.log("[TM-Vite-JS-Template] 面板已关闭");
    });
    const bodyEl = panel.querySelector(".tm-panel__body");
    const toggleBtn = panel.querySelector("#tm-toggle");
    toggleBtn.addEventListener("click", () => {
      if (bodyEl.style.display === "none") {
        bodyEl.style.display = "";
        toggleBtn.textContent = "—";
      } else {
        bodyEl.style.display = "none";
        toggleBtn.textContent = "+";
      }
    });
    const masterSwitch = panel.querySelector("#tm-master-switch");
    const masterEnabled = storage2.get("master_enabled", true);
    masterSwitch.checked = masterEnabled;
    masterSwitch.addEventListener("change", (e) => {
      const enabled = e.target.checked;
      storage2.set("master_enabled", enabled);
      const moduleItems = panel.querySelectorAll(".tm-module-item");
      moduleItems.forEach((item) => {
        const moduleSwitch = item.querySelector(".tm-module-switch");
        const runBtn = item.querySelector(".tm-module-run");
        moduleSwitch.disabled = !enabled;
        runBtn.disabled = !enabled;
        item.classList.toggle("disabled", !enabled);
      });
      const runAllBtn2 = panel.querySelector("#tm-run-all");
      runAllBtn2.disabled = !enabled;
      console.log(`[脚本框架] ${enabled ? "已启用" : "已禁用"}`);
    });
    const runAllBtn = panel.querySelector("#tm-run-all");
    runAllBtn.disabled = !masterEnabled;
    runAllBtn.addEventListener("click", () => {
      if (masterSwitch.checked) {
        runAllModules();
      }
    });
  }
  function updateModulesList(panel) {
    const modulesList = panel.querySelector("#tm-modules-list");
    const modules = getAllModules();
    const masterSwitch = panel.querySelector("#tm-master-switch");
    const masterEnabled = masterSwitch.checked;
    if (modules.length === 0) {
      modulesList.innerHTML = '<div class="tm-no-modules">暂无模块</div>';
      return;
    }
    modulesList.innerHTML = modules.map((module) => {
      const fileName = module.filePath ? module.filePath.split("/").pop().replace(".js", "") : module.name;
      return `
      <div class="tm-module-item ${!masterEnabled ? "disabled" : ""}" data-module="${module.name}">
        <div class="tm-module-info">
          <div class="tm-module-name">${module.name}</div>
          <div class="tm-module-file">${fileName}.js</div>
        </div>
        <div class="tm-module-controls">
          <label class="tm-switch tm-switch-small">
            <input type="checkbox" class="tm-module-switch" ${module.enabled ? "checked" : ""} ${!masterEnabled ? "disabled" : ""}>
            <span class="tm-slider"></span>
          </label>
          <button class="tm-btn tm-btn-small tm-module-run" ${!masterEnabled ? "disabled" : ""}>运行</button>
        </div>
      </div>
    `;
    }).join("");
    modules.forEach((module) => {
      const moduleItem = modulesList.querySelector(`[data-module="${module.name}"]`);
      const moduleSwitch = moduleItem.querySelector(".tm-module-switch");
      const runBtn = moduleItem.querySelector(".tm-module-run");
      moduleSwitch.addEventListener("change", (e) => {
        const enabled = e.target.checked;
        toggleModule(module.name, enabled);
        runBtn.disabled = !enabled || !masterEnabled;
      });
      runBtn.addEventListener("click", () => {
        if (masterEnabled && module.enabled) {
          runModule(module.name);
        }
      });
      runBtn.disabled = !module.enabled || !masterEnabled;
    });
  }
  function dragElement(el) {
    const header = el.querySelector(".tm-panel__header");
    let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
    header.onmousedown = dragMouseDown;
    header.ondblclick = () => {
      el.style.top = "20px";
      el.style.right = "20px";
      el.style.left = "auto";
    };
    function dragMouseDown(e) {
      e = e || window.event;
      e.preventDefault();
      pos3 = e.clientX;
      pos4 = e.clientY;
      document.onmouseup = closeDragElement;
      document.onmousemove = elementDrag;
    }
    function elementDrag(e) {
      e = e || window.event;
      e.preventDefault();
      pos1 = pos3 - e.clientX;
      pos2 = pos4 - e.clientY;
      pos3 = e.clientX;
      pos4 = e.clientY;
      el.style.top = el.offsetTop - pos2 + "px";
      el.style.left = el.offsetLeft - pos1 + "px";
    }
    function closeDragElement() {
      document.onmouseup = null;
      document.onmousemove = null;
    }
  }
  class GMStorage {
    constructor(prefix = "") {
      this.prefix = prefix;
    }
    _key(k) {
      return this.prefix + k;
    }
    get(key, def = null) {
      try {
        return GM_getValue(this._key(key), def);
      } catch (e) {
        console.warn("GM_getValue failed, fallback localStorage", e);
        const v = localStorage.getItem(this._key(key));
        return v ? JSON.parse(v) : def;
      }
    }
    set(key, value) {
      try {
        GM_setValue(this._key(key), value);
      } catch (e) {
        console.warn("GM_setValue failed, fallback localStorage", e);
        localStorage.setItem(this._key(key), JSON.stringify(value));
      }
    }
  }
  (async function() {
    console.log("[TM-Vite-JS-Template] Script start");
    const storage2 = new GMStorage("tm_template_");
    await registerModules({ storage: storage2 });
    initPanel({ storage: storage2 });
    const masterEnabled = storage2.get("master_enabled", true);
    if (masterEnabled) {
      console.log("[TM-Vite-JS-Template] 主开关已启用，自动运行所有模块");
      runAllModules();
    } else {
      console.log("[TM-Vite-JS-Template] 主开关已禁用，跳过自动运行");
    }
  })();

})();