!function(){function e(e,t){if(-1!==e.indexOf("\\")&&(e=e.replace(i,"/")),"/"===e[0]&&"/"===e[1])return t.slice(0,t.indexOf(":")+1)+e;if("."===e[0]&&("/"===e[1]||"."===e[1]&&("/"===e[2]||2===e.length&&(e+="/"))||1===e.length&&(e+="/"))||"/"===e[0]){var n,s=t.slice(0,t.indexOf(":")+1);if(n="/"===t[s.length+1]?"file:"!==s?(n=t.slice(s.length+2)).slice(n.indexOf("/")+1):t.slice(8):t.slice(s.length+("/"===t[s.length])),"/"===e[0])return t.slice(0,t.length-n.length-1)+e;for(var r=n.slice(0,n.lastIndexOf("/")+1)+e,l=[],o=-1,c=0;c<r.length;c++)-1!==o?"/"===r[c]&&(l.push(r.slice(o,c+1)),o=-1):"."===r[c]?"."!==r[c+1]||"/"!==r[c+2]&&c+2!==r.length?"/"===r[c+1]||c+1===r.length?c+=1:o=c:(l.pop(),c+=2):o=c;return-1!==o&&l.push(r.slice(o)),t.slice(0,t.length-n.length)+l.join("")}}var t;if("undefined"!=typeof document){var n=document.querySelector("base[href]");n&&(t=n.href)}if(!t&&"undefined"!=typeof location){var s=(t=location.href.split("#")[0].split("?")[0]).lastIndexOf("/");-1!==s&&(t=t.slice(0,s+1))}var i=/\\/g;!function(t){var n=t.System.constructor.prototype,s=/^[^#?]+\.(css|html|json|wasm)([?#].*)?$/,i=n.shouldFetch.bind(n);n.shouldFetch=function(e){return i(e)||s.test(e)};var r=/^application\/json(;|$)/,l=/^text\/css(;|$)/,o=/^application\/wasm(;|$)/,c=n.fetch;n.fetch=function(n,s){return c(n,s).then((function(i){if(s.passThrough)return i;if(!i.ok)return i;var c=i.headers.get("content-type");return r.test(c)?i.json().then((function(e){return new Response(new Blob(['System.register([],function(e){return{execute:function(){e("default",'+JSON.stringify(e)+")}}})"],{type:"application/javascript"}))})):l.test(c)?i.text().then((function(t){return t=t.replace(/url\(\s*(?:(["'])((?:\\.|[^\n\\"'])+)\1|((?:\\.|[^\s,"'()\\])+))\s*\)/g,(function(t,s,i,r){return["url(",s,(l=i||r,o=n,e(l,o)||(-1!==l.indexOf(":")?l:e("./"+l,o))),s,")"].join("");var l,o})),new Response(new Blob(["System.register([],function(e){return{execute:function(){var s=new CSSStyleSheet();s.replaceSync("+JSON.stringify(t)+');e("default",s)}}})'],{type:"application/javascript"}))})):o.test(c)?(WebAssembly.compileStreaming?WebAssembly.compileStreaming(i):i.arrayBuffer().then(WebAssembly.compile)).then((function(e){t.System.wasmModules||(t.System.wasmModules=Object.create(null)),t.System.wasmModules[n]=e;var s=[],i=[];return WebAssembly.Module.imports&&WebAssembly.Module.imports(e).forEach((function(e){var t=JSON.stringify(e.module);-1===s.indexOf(t)&&(s.push(t),i.push("function(m){i["+t+"]=m}"))})),new Response(new Blob(["System.register(["+s.join(",")+"],function(e){var i={};return{setters:["+i.join(",")+"],execute:function(){return WebAssembly.instantiate(System.wasmModules["+JSON.stringify(n)+"],i).then(function(m){e(m.exports)})}}})"],{type:"application/javascript"}))})):i}))}}("undefined"!=typeof self?self:global)}();
//# sourceMappingURL=module-types.min.js.map
