---
type: "always_apply"
---

# Cursor Rules for TM Vite Project

> 本规则用于指导 Cursor AI 在本项目（Tampermonkey + Vite + vite-plugin-monkey）中的自动化开发流程、代码风格与错误修复。

---

## 1. 项目背景
- 本项目使用 **Vite + vite-plugin-monkey** 开发 Tampermonkey 脚本（JS 版本）。
- 目录结构固定：`src/` 下分为 `ui/`、`modules/`、`utils/` 三类文件。
- 入口文件：`src/main.js`；模块注册统一在 `src/modules/index.js`。

---

## 2. 总体目标
- **自动化生成/修改模块文件**，并自动在模块中心注册。
- **保持统一代码风格**，减少无意义的样式变动。
- **出错时自动排查与修复**，再重试命令。

---

## 3. 代码风格（JavaScript）
- 统一使用 `single quotes`。
- 默认不加分号（semi=false）。
- 文件命名使用小写中横线或小驼峰：`example-module.js` / `exampleModule.js`，目录同理。
- 一般不引入全局依赖；如需依赖，请在 `package.json` 中声明，并使用 `import`。

---

## 4. 关键约定
- **模块导出统一结构**：
  ```js
  export default function moduleName(ctx) {
    return {
      name: 'moduleName',
      run() {
        // ...
      }
    }
  }
