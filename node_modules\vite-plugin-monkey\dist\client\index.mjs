// src/client/window.ts
var key = `__monkeyWindow-` + (() => {
  try {
    return new URL(import.meta.url).origin;
  } catch {
    return location.origin;
  }
})();
var monkeyWindow = document[key] ?? window;

// src/client/context.ts
var unsafeWindow = /* @__PURE__ */ (() => monkeyWindow.unsafeWindow)();
var GM = /* @__PURE__ */ (() => monkeyWindow.GM)();
var GM_info = /* @__PURE__ */ (() => monkeyWindow.GM_info)();
var GM_log = /* @__PURE__ */ (() => monkeyWindow.GM_log)();
var GM_getValue = /* @__PURE__ */ (() => monkeyWindow.GM_getValue)();
var GM_getValues = /* @__PURE__ */ (() => monkeyWindow.GM_getValues)();
var GM_setValue = /* @__PURE__ */ (() => monkeyWindow.GM_setValue)();
var GM_setValues = /* @__PURE__ */ (() => monkeyWindow.GM_setValues)();
var GM_deleteValue = /* @__PURE__ */ (() => monkeyWindow.GM_deleteValue)();
var GM_deleteValues = /* @__PURE__ */ (() => monkeyWindow.GM_deleteValues)();
var GM_listValues = /* @__PURE__ */ (() => monkeyWindow.GM_listValues)();
var GM_addValueChangeListener = /* @__PURE__ */ (() => monkeyWindow.GM_addValueChangeListener)();
var GM_removeValueChangeListener = /* @__PURE__ */ (() => monkeyWindow.GM_removeValueChangeListener)();
var GM_getResourceText = /* @__PURE__ */ (() => monkeyWindow.GM_getResourceText)();
var GM_getResourceURL = /* @__PURE__ */ (() => monkeyWindow.GM_getResourceURL)();
var GM_addElement = /* @__PURE__ */ (() => monkeyWindow.GM_addElement)();
var GM_addStyle = /* @__PURE__ */ (() => monkeyWindow.GM_addStyle)();
var GM_openInTab = /* @__PURE__ */ (() => monkeyWindow.GM_openInTab)();
var GM_getTab = /* @__PURE__ */ (() => monkeyWindow.GM_getTab)();
var GM_saveTab = /* @__PURE__ */ (() => monkeyWindow.GM_saveTab)();
var GM_getTabs = /* @__PURE__ */ (() => monkeyWindow.GM_getTabs)();
var GM_registerMenuCommand = /* @__PURE__ */ (() => monkeyWindow.GM_registerMenuCommand)();
var GM_unregisterMenuCommand = /* @__PURE__ */ (() => monkeyWindow.GM_unregisterMenuCommand)();
var GM_notification = /* @__PURE__ */ (() => monkeyWindow.GM_notification)();
var GM_setClipboard = /* @__PURE__ */ (() => monkeyWindow.GM_setClipboard)();
var GM_xmlhttpRequest = /* @__PURE__ */ (() => monkeyWindow.GM_xmlhttpRequest)();
var GM_download = /* @__PURE__ */ (() => monkeyWindow.GM_download)();
var GM_webRequest = /* @__PURE__ */ (() => monkeyWindow.GM_webRequest)();
var GM_cookie = /* @__PURE__ */ (() => monkeyWindow.GM_cookie)();
export {
  GM,
  GM_addElement,
  GM_addStyle,
  GM_addValueChangeListener,
  GM_cookie,
  GM_deleteValue,
  GM_deleteValues,
  GM_download,
  GM_getResourceText,
  GM_getResourceURL,
  GM_getTab,
  GM_getTabs,
  GM_getValue,
  GM_getValues,
  GM_info,
  GM_listValues,
  GM_log,
  GM_notification,
  GM_openInTab,
  GM_registerMenuCommand,
  GM_removeValueChangeListener,
  GM_saveTab,
  GM_setClipboard,
  GM_setValue,
  GM_setValues,
  GM_unregisterMenuCommand,
  GM_webRequest,
  GM_xmlhttpRequest,
  monkeyWindow,
  unsafeWindow
};
//# sourceMappingURL=index.mjs.map