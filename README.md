# TM-Vite-JS-Template

一个基于 Vite 的现代化 Tampermonkey 脚本开发框架，支持模块化开发、热更新和完整的管理界面。

## 特性
- 自动生成 Tampermonkey 元信息
- 热更新（HMR）与自动打开安装脚本页面
- 模块化代码结构，易扩展
- 可拖拽 UI 面板

## 使用步骤
1. `npm install`
2. `npm run dev` → 浏览器自动打开安装脚本
3. 在 Tampermonkey 内点击“安装”
4. 修改 `src` 目录内容 → 自动生效
5. `npm run build` → 生成发布版 `.user.js`

## 目录说明
- `src/ui`：UI 相关代码
- `src/modules`：功能模块化代码
- `src/utils`：工具函数封装（如数据存储）

## 常见问题
- **缓存问题**：开发环境用 HMR 不会缓存；生产发布时使用固定版本号即可
- **GM_* API 不可用？** 检查 `vite.config.js` 的 `grant`，或在 Tampermonkey 中打开增强权限

欢迎根据自己需求调整。
