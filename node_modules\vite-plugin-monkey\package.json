{"name": "vite-plugin-monkey", "version": "6.0.0", "description": "A vite plugin server and build your.user.js for userscript engine like Tampermonkey and Violentmonkey and Greasemonkey", "main": "dist/node/index.mjs", "types": "dist/node/index.d.ts", "type": "module", "sideEffects": false, "files": ["dist", "global.d.ts", "client.d.ts"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["vite", "vite-plugin", "monkey", "Tam<PERSON>mon<PERSON>", "Violentmonkey", "Greasemonkey", "userscript", "greasyfork", "typescript"], "author": "lisonge", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/lisonge/vite-plugin-monkey.git", "directory": "packages/vite-plugin-monkey"}, "bugs": {"url": "https://github.com/lisonge/vite-plugin-monkey/issues"}, "homepage": "https://github.com/lisonge/vite-plugin-monkey#readme", "dependencies": {"acorn-walk": "^8.3.4", "cross-spawn": "^7.0.6", "htmlparser2": "^10.0.0", "import-meta-resolve": "^4.1.0", "magic-string": "^0.30.17", "mrmime": "^2.0.1", "open": "^10.2.0", "picocolors": "^1.1.1", "postcss-url": "^10.1.3", "systemjs": "^6.15.1"}, "peerDependencies": {"vite": "^6.0.0 || ^7.0.0"}, "peerDependenciesMeta": {"vite": {"optional": true}}, "volta": {"extends": "../../package.json"}, "scripts": {"prebuild": "tsc", "build": "tsup", "postbuild": "tsx ./scripts/postbuild.ts"}}