{"name": "xxhashjs", "version": "0.2.2", "description": "xxHash in Javascript", "main": "./lib/index.js", "scripts": {"test": "mocha", "prepublish": "webpack && uglifyjs -m -c -o build/xxhash.min.js build/xxhash.js"}, "repository": {"type": "git", "url": "https://github.com/pierrec/js-xxhash"}, "keywords": ["xxhash", "xxh"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/pierrec/js-xxhash/issues"}, "homepage": "https://github.com/pierrec/js-xxhash", "dependencies": {"cuint": "^0.2.2"}, "devDependencies": {"benchmark": "*", "uglifyjs": "^2.4.11", "webpack": "^3.10.0"}}