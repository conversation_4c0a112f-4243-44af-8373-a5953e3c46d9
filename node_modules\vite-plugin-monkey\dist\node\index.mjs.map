{"version": 3, "sources": ["../../src/node/plugins/buildBundle.ts", "../../src/node/utils/gmApi.ts", "../../src/node/userscript/index.ts", "../../src/node/utils/grant.ts", "../../src/node/utils/others.ts", "../../src/node/utils/systemjs.ts", "../../src/node/utils/topLevelAwait.ts", "../../src/node/plugins/config.ts", "../../src/node/plugins/externalGlobals.ts", "../../src/node/utils/pkg.ts", "../../src/node/plugins/externalLoader.ts", "../../src/node/plugins/externalResource.ts", "../../src/node/plugins/fixAssetUrl.ts", "../../src/node/plugins/fixClient.ts", "../../src/node/plugins/fixCssUrl.ts", "../../src/node/plugins/perview.ts", "../../src/node/utils/template.ts", "../../src/node/plugins/redirectClient.ts", "../../src/node/plugins/server.ts", "../../src/node/utils/openBrowser.ts", "../../src/node/plugins/virtualHtml.ts", "../../src/node/plugins/index.ts", "../../src/node/cdn.ts", "../../src/node/utils/option.ts", "../../src/node/index.ts"], "sourcesContent": ["import type { OutputChunk, RollupOutput } from 'rollup';\nimport type { Plugin, ResolvedConfig } from 'vite';\nimport { build } from 'vite';\nimport { finalMonkeyOptionToComment } from '../userscript';\nimport { collectGrant } from '../utils/grant';\nimport {\n  getInjectCssCode,\n  moduleExportExpressionWrapper,\n} from '../utils/others';\nimport { getSystemjsRequireUrls, getSystemjsTexts } from '../utils/systemjs';\nimport {\n  getSafeTlaIdentifier,\n  transformIdentifierToTla,\n  transformTlaToIdentifier,\n} from '../utils/topLevelAwait';\nimport type { ResolvedMonkeyOption } from '../utils/types';\n\nconst __entry_name = `__monkey.entry.js`;\n\n// https://github.com/vitejs/vite/blob/main/packages/plugin-legacy/src/index.ts\nconst polyfillId = '\\0vite/legacy-polyfills';\n\nconst systemJsImportMapPrefix = `user`;\n\nexport const buildBundleFactory = (\n  getOption: () => Promise<ResolvedMonkeyOption>,\n): Plugin => {\n  let option: ResolvedMonkeyOption;\n  let viteConfig: ResolvedConfig;\n  return {\n    name: 'monkey:buildBundle',\n    apply: 'build',\n    enforce: 'post',\n    async config() {\n      option = await getOption();\n    },\n    async configResolved(resolvedConfig) {\n      viteConfig = resolvedConfig;\n    },\n    async generateBundle(_, rawBundle) {\n      const entryChunks: OutputChunk[] = [];\n      const chunks: OutputChunk[] = [];\n      Object.values(rawBundle).forEach((chunk) => {\n        if (chunk.type == 'chunk') {\n          if (chunk.facadeModuleId != polyfillId) {\n            chunks.push(chunk);\n          }\n          if (chunk.isEntry) {\n            if (chunk.facadeModuleId == polyfillId) {\n              entryChunks.unshift(chunk);\n            } else {\n              entryChunks.push(chunk);\n            }\n          }\n        }\n      });\n\n      const fristEntryChunk = entryChunks.find(\n        (s) => s.facadeModuleId != polyfillId,\n      );\n\n      const hasDynamicImport = entryChunks.some(\n        (e) => e.dynamicImports.length > 0,\n      );\n\n      const usedModules = new Set<string>();\n\n      const tlaIdentifier = getSafeTlaIdentifier(rawBundle);\n\n      const buildResult = (await build({\n        logLevel: 'error',\n        configFile: false,\n        esbuild: false,\n        plugins: [\n          {\n            name: 'monkey:mock',\n            enforce: 'pre',\n            resolveId(source, importer, options) {\n              if (!importer && options.isEntry) {\n                return '\\0' + source;\n              }\n              const chunk = Object.values(rawBundle).find(\n                (chunk) =>\n                  chunk.type == 'chunk' && source.endsWith(chunk.fileName),\n              ) as OutputChunk | undefined;\n              if (chunk) {\n                return '\\0' + source;\n              }\n            },\n            async load(id) {\n              if (!id.startsWith('\\0')) return;\n\n              if (id.endsWith(__entry_name)) {\n                return entryChunks\n                  .map((a) => `import ${JSON.stringify(`./${a.fileName}`)};`)\n                  .join('\\n');\n              }\n              const [k, chunk] =\n                Object.entries(rawBundle).find(([_, chunk]) =>\n                  id.endsWith(chunk.fileName),\n                ) ?? [];\n              if (chunk && chunk.type == 'chunk' && k) {\n                usedModules.add(k);\n                if (!hasDynamicImport) {\n                  const ch = transformTlaToIdentifier(\n                    this,\n                    chunk,\n                    tlaIdentifier,\n                  );\n                  if (ch) return ch;\n                }\n                return {\n                  code: chunk.code,\n                  map: chunk.map,\n                };\n              }\n            },\n            generateBundle(_, iifeBundle) {\n              if (hasDynamicImport) {\n                return;\n              }\n              Object.entries(iifeBundle).forEach(([_, chunk]) => {\n                transformIdentifierToTla(this, chunk, tlaIdentifier);\n              });\n            },\n          },\n        ],\n        build: {\n          write: false,\n          minify: false,\n          target: 'esnext',\n          rollupOptions: {\n            external(source) {\n              return source in option.globalsPkg2VarName;\n            },\n            output: {\n              globals: option.globalsPkg2VarName,\n            },\n          },\n          lib: {\n            entry: __entry_name,\n            formats: [hasDynamicImport ? 'system' : 'iife'] as any,\n            name: hasDynamicImport ? undefined : '__expose__',\n            fileName: () => `__entry.js`,\n          },\n        },\n      })) as RollupOutput[];\n      usedModules.forEach((k) => {\n        if (fristEntryChunk != rawBundle[k]) {\n          delete rawBundle[k];\n        }\n      });\n\n      const buildBundle = buildResult[0].output.flat();\n      let finalJsCode = ``;\n      if (hasDynamicImport) {\n        const systemJsModules: string[] = [];\n        let entryName = '';\n        Object.entries(buildBundle).forEach(([_, chunk]) => {\n          if (chunk.type == 'chunk') {\n            const name = JSON.stringify(`./` + chunk.fileName);\n            systemJsModules.push(\n              chunk.code\n                .trimStart()\n                .replace(/^System\\.register\\(/, `System.register(${name}, `),\n            );\n            if (chunk.isEntry) {\n              entryName = name;\n            }\n          }\n        });\n        systemJsModules.push(`System.import(${entryName}, \"./\");`);\n        finalJsCode = systemJsModules.join('\\n');\n        const usedModuleIds = Array.from(this.getModuleIds()).filter(\n          (d) => d in option.globalsPkg2VarName,\n        );\n        // {vue:'xxx:vue'}\n        const importsMap = usedModuleIds.reduce(\n          (p: Record<string, string>, c) => {\n            p[c] = `${systemJsImportMapPrefix}:${c}`;\n            return p;\n          },\n          {},\n        );\n        // inject SystemJs external globals\n        finalJsCode = [\n          Object.keys(importsMap).length > 0\n            ? `System.addImportMap({ imports: ${JSON.stringify(importsMap)} });`\n            : ``,\n          ...usedModuleIds.map(\n            (id) =>\n              `System.set(${JSON.stringify(\n                `${systemJsImportMapPrefix}:${id}`,\n              )}, ${moduleExportExpressionWrapper(\n                option.globalsPkg2VarName[id],\n              )});`,\n          ),\n          '\\n' + finalJsCode,\n        ]\n          .filter((s) => s)\n          .join('\\n');\n\n        if (typeof option.systemjs == 'function') {\n          option.collectRequireUrls.push(\n            ...getSystemjsRequireUrls(option.systemjs),\n          );\n        } else {\n          finalJsCode =\n            (await getSystemjsTexts()).join('\\n') + '\\n' + finalJsCode;\n        }\n      } else {\n        // use iife\n        Object.entries(buildBundle).forEach(([_, chunk]) => {\n          if (chunk.type == 'chunk' && chunk.isEntry) {\n            finalJsCode = chunk.code;\n          }\n        });\n      }\n\n      const injectCssCode = await getInjectCssCode(option, rawBundle);\n\n      let collectGrantSet: Set<string>;\n      if (option.build.autoGrant) {\n        collectGrantSet = collectGrant(\n          this,\n          chunks,\n          injectCssCode,\n          viteConfig.build.minify !== false,\n        );\n      } else {\n        collectGrantSet = new Set<string>();\n      }\n\n      const comment = await finalMonkeyOptionToComment(\n        option,\n        collectGrantSet,\n        'build',\n      );\n\n      const mergedCode = [comment, injectCssCode, finalJsCode]\n        .filter((s) => s)\n        .join(`\\n\\n`)\n        .trimEnd();\n      if (fristEntryChunk) {\n        fristEntryChunk.fileName = option.build.fileName;\n        fristEntryChunk.code = mergedCode;\n      } else {\n        this.emitFile({\n          type: 'asset',\n          fileName: option.build.fileName,\n          source: mergedCode,\n        });\n      }\n\n      if (option.build.metaFileName) {\n        this.emitFile({\n          type: 'asset',\n          fileName: option.build.metaFileName(),\n          source: await finalMonkeyOptionToComment(\n            option,\n            collectGrantSet,\n            'meta',\n          ),\n        });\n      }\n    },\n  };\n};\n", "export const gmIdentifiers = [\n  'GM_addElement',\n  'GM_addStyle',\n  'GM_addValueChangeListener',\n  'GM_cookie',\n  'GM_deleteValue',\n  'GM_deleteValues',\n  'GM_download',\n  'GM_getResourceText',\n  'GM_getResourceURL',\n  'GM_getTab',\n  'GM_getTabs',\n  'GM_getValue',\n  'GM_getValues',\n  'GM_info',\n  'GM_listValues',\n  'GM_log',\n  'GM_notification',\n  'GM_openInTab',\n  'GM_registerMenuCommand',\n  'GM_removeValueChangeListener',\n  'GM_saveTab',\n  'GM_setClipboard',\n  'GM_setValue',\n  'GM_setValues',\n  'GM_unregisterMenuCommand',\n  'GM_webRequest',\n  'GM_xmlhttpRequest',\n] as const;\n\nconst gmMembers = [\n  'GM.addElement',\n  'GM.addStyle',\n  'GM.addValueChangeListener',\n  'GM.cookie',\n  'GM.deleteValue',\n  'GM.deleteValues',\n  'GM.download',\n  'GM.getResourceText',\n  // https://www.tampermonkey.net/documentation.php#api:GM_getResourceURL\n  'GM.getResourceUrl',\n  'GM.getTab',\n  'GM.getTabs',\n  'GM.getValue',\n  'GM.getValues',\n  'GM.info',\n  'GM.listValues',\n  'GM.log',\n  'GM.notification',\n  'GM.openInTab',\n  'GM.registerMenuCommand',\n  'GM.removeValueChangeListener',\n  'GM.saveTab',\n  'GM.setClipboard',\n  'GM.setValue',\n  'GM.setValues',\n  'GM.unregisterMenuCommand',\n  'GM.webRequest',\n  'GM.xmlHttpRequest',\n] as const;\n\nconst othersGrantNames = [\n  'unsafeWindow',\n  'window.close',\n  'window.focus',\n  'window.onurlchange',\n] as const;\n\nexport type GrantType =\n  | (typeof gmIdentifiers)[number]\n  | (typeof gmMembers)[number]\n  | (typeof othersGrantNames)[number];\n\nexport const grantNames = [...gmMembers, ...gmIdentifiers, ...othersGrantNames];\n", "import { grantNames, type GrantType } from '../utils/gmApi';\nimport type { ResolvedMonkeyOption, IArray, LocaleType } from '../utils/types';\nimport type { GreasemonkeyUserScript, GreaseRunAt } from './greasemonkey';\nimport type {\n  AntifeatureType,\n  TampermonkeyUserScript,\n  TamperRunAt,\n} from './tampermonkey';\nimport type { ViolentmonkeyUserScript, ViolentRunAt } from './violentmonkey';\nimport type { ViolentInjectInto } from './violentmonkey';\n\nexport type {\n  GreasemonkeyUserScript,\n  TampermonkeyUserScript,\n  ViolentmonkeyUserScript,\n};\n\n/**\n * @see https://greasyfork.org/help/meta-keys\n */\ninterface GreasyforkUserScript {\n  /**\n   * @see https://greasyfork.org/help/meta-keys\n   * @default package.json.license\n   */\n  license?: string;\n\n  /**\n   * @see https://greasyfork.org/help/meta-keys\n   */\n  contributionURL?: string;\n\n  /**\n   * @see https://greasyfork.org/help/meta-keys\n   */\n  contributionAmount?: string;\n\n  /**\n   * @see https://greasyfork.org/help/meta-keys\n   */\n  compatible?: string;\n\n  /**\n   * @see https://greasyfork.org/help/meta-keys\n   */\n  incompatible?: string;\n}\n\ninterface MergemonkeyUserScript {\n  /**\n   * @default package.json.name??'monkey'\n   * @default {...{'':package.json.name??'monkey'},...name} // if name is object\n   */\n  name?: string | LocaleType<string>;\n\n  namespace?: string;\n\n  /**\n   * @default package.json.version\n   */\n  version?: string;\n\n  /**\n   * @default package.json.description\n   * @default {...{'':package.json.description},...description} // if description is object\n   */\n  description?: string | LocaleType<string>;\n\n  /**\n   * @default package.json.author\n   */\n  author?: string;\n\n  /**\n   * @default package.json.homepage\n   */\n  homepage?: string;\n\n  /**\n   * @default package.json.homepage\n   */\n  homepageURL?: string;\n\n  /**\n   * @default package.json.repository\n   */\n  source?: string;\n\n  /**\n   * @default package.json.bugs\n   */\n  supportURL?: string;\n\n  /**\n   * @see https://wiki.greasespot.net/Metadata_Block#.40run-at\n   *\n   * @see https://www.tampermonkey.net/documentation.php#meta:run_at\n   *\n   * @see https://violentmonkey.github.io/api/metadata-block/#run-at\n   *\n   */\n  'run-at'?: GreaseRunAt | TamperRunAt | ViolentRunAt;\n\n  /**\n   * @see https://wiki.greasespot.net/Metadata_Block#.40grant\n   *\n   * @see https://www.tampermonkey.net/documentation.php#meta:grant\n   *\n   * @see https://violentmonkey.github.io/api/metadata-block/#grant\n   *\n   * if set '\\*', will add all GM_* to UserScript\n   */\n  grant?: IArray<GrantType> | 'none' | '*';\n\n  /**\n   * custom extra meta\n   * @example\n   * [['antifeature', ['miner', 'hello233']]]\n   * // -->\n   * // \\@antifeature  miner     hello233\n   */\n  $extra?: [string, IArray<string>][] | Record<string, IArray<string>>;\n}\n\n/**\n * UserScript, merge metadata from Greasemonkey, Tampermonkey, Violentmonkey, Greasyfork\n */\nexport type MonkeyUserScript = GreasemonkeyUserScript &\n  TampermonkeyUserScript &\n  ViolentmonkeyUserScript &\n  GreasyforkUserScript &\n  MergemonkeyUserScript;\n\nexport interface FinalUserScript extends GreasyforkUserScript {\n  name: LocaleType<string>;\n  namespace?: string;\n  version?: string;\n  description: LocaleType<string>;\n  icon?: string;\n  include: string[];\n  match: string[];\n  exclude: string[];\n  require: string[];\n  resource: Record<string, string>;\n  noframes: boolean;\n  unwrap: boolean;\n  webRequest: string[];\n\n  author?: string;\n  copyright?: string;\n  homepage?: string;\n  homepageURL?: string;\n  website?: string;\n  source?: string;\n  iconURL?: string;\n  defaulticon?: string;\n  icon64?: string;\n  icon64URL?: string;\n  updateURL?: string;\n  downloadURL?: string;\n  supportURL?: string;\n  connect: string[];\n  sandbox?: string;\n  tag: string[];\n  antifeature: AntifeatureType[];\n\n  'exclude-match': string[];\n  'inject-into'?: ViolentInjectInto;\n  'run-at'?: GreaseRunAt | TamperRunAt | ViolentRunAt;\n  grant: Set<string>;\n  $extra: [string, ...string[]][];\n}\n\nexport const finalMonkeyOptionToComment = async (\n  option: ResolvedMonkeyOption,\n  collectGrantSet: Set<string>,\n  mode: `serve` | `build` | `meta`,\n): Promise<string> => {\n  const { userscript, collectRequireUrls, collectResource } = option;\n  let attrList: [string, ...string[]][] = [];\n  const {\n    name,\n    namespace,\n    version,\n    author,\n    description,\n    license,\n    copyright,\n\n    icon,\n    iconURL,\n    icon64,\n    icon64URL,\n    defaulticon,\n\n    homepage,\n    homepageURL,\n    website,\n    source,\n\n    supportURL,\n    downloadURL,\n    updateURL,\n\n    include,\n    match,\n    exclude,\n    require,\n    'exclude-match': excludeMatch,\n\n    'inject-into': injectInto,\n    'run-at': runAt,\n\n    compatible,\n    incompatible,\n\n    antifeature,\n    contributionAmount,\n    contributionURL,\n\n    connect,\n    sandbox,\n    tag,\n    resource,\n    grant,\n    noframes,\n    unwrap,\n    webRequest,\n    $extra,\n  } = userscript;\n  Object.entries({\n    namespace,\n    version,\n    author,\n    license,\n    copyright,\n    icon,\n    iconURL,\n    icon64,\n    icon64URL,\n    defaulticon,\n    homepage,\n    homepageURL,\n    website,\n    source,\n    supportURL,\n    downloadURL,\n    updateURL,\n    'inject-into': injectInto,\n    'run-at': runAt,\n    compatible,\n    incompatible,\n    contributionAmount,\n    contributionURL,\n    sandbox,\n  }).forEach(([k, v]) => {\n    if (typeof v == 'string') {\n      attrList.push([k, v]);\n    }\n  });\n  Object.entries(name).forEach(([k, v]) => {\n    if (k == '') {\n      attrList.push(['name', v]);\n    } else {\n      attrList.push(['name:' + k, v]);\n    }\n  });\n  Object.entries(description).forEach(([k, v]) => {\n    if (k == '') {\n      attrList.push(['description', v]);\n    } else {\n      attrList.push(['description:' + k, v]);\n    }\n  });\n\n  Object.entries({\n    include,\n    match,\n    exclude,\n    'exclude-match': excludeMatch,\n  }).forEach(([k, v]) => {\n    v.forEach((v2) => {\n      attrList.push([k, v2]);\n    });\n  });\n\n  [...require, ...collectRequireUrls].forEach((s) => {\n    attrList.push(['require', s]);\n  });\n\n  Object.entries({ ...resource, ...collectResource }).forEach(([k, v]) => {\n    attrList.push(['resource', k, v]);\n  });\n\n  connect.forEach((s) => {\n    attrList.push(['connect', s]);\n  });\n  tag.forEach((s) => {\n    attrList.push(['tag', s]);\n  });\n\n  webRequest.forEach((s) => {\n    attrList.push(['webRequest', s]);\n  });\n\n  if (grant.has('none')) {\n    attrList.push(['grant', 'none']);\n  } else if (grant.has('*')) {\n    grantNames.forEach((s) => {\n      attrList.push(['grant', s]);\n    });\n  } else {\n    new Set([...Array.from(collectGrantSet.values()).flat(), ...grant]).forEach(\n      (s) => {\n        if (!s.trim()) return;\n        attrList.push(['grant', s]);\n      },\n    );\n  }\n  antifeature.forEach(({ description, type, tag }) => {\n    attrList.push([\n      tag ? `antifeature:${tag}` : 'antifeature',\n      type,\n      description,\n    ]);\n  });\n  if (noframes) {\n    attrList.push(['noframes']);\n  }\n  if (unwrap) {\n    attrList.push(['unwrap']);\n  }\n  attrList.push(...$extra);\n\n  attrList = defaultSortFormat(attrList);\n\n  // format\n  if (option.align >= 1) {\n    const formatKey = (subAttrList: [string, ...string[]][]) => {\n      if (subAttrList.length == 0) return;\n      const maxLen = Math.max(...subAttrList.map((s) => s[1].length));\n      subAttrList.forEach((s) => {\n        s[1] = s[1].padEnd(option.align + maxLen);\n      });\n    };\n\n    formatKey(attrList.filter((s) => s[0] == 'resource'));\n    formatKey(\n      attrList.filter(\n        (s) => s[0] == 'antifeature' || s[0].startsWith('antifeature:'),\n      ),\n    );\n    // format all\n    const maxLen = Math.max(...attrList.map((s) => s[0].length));\n    attrList.forEach((s) => {\n      s[0] = s[0].padEnd(option.align + maxLen);\n    });\n  }\n\n  const uString = [\n    '==UserScript==',\n    ...attrList.map(\n      (attr) =>\n        '@' +\n        attr\n          .map((v) => {\n            return v.endsWith('\\x20') ? v : v + '\\x20';\n          })\n          .join('')\n          .trimEnd(),\n    ),\n    '==/UserScript==',\n  ]\n    .map((s) => '//\\x20' + s)\n    .join('\\n');\n\n  return option.generate({ userscript: uString, mode });\n};\n\nconst stringSort = (a: [string, ...string[]], b: [string, ...string[]]) => {\n  const minLen = Math.min(a.length, b.length);\n  for (let i = 0; i < minLen; i++) {\n    if (a[i] > b[i]) {\n      return 1;\n    } else if (a[i] < b[i]) {\n      return -1;\n    }\n  }\n  if (a.length > b.length) {\n    return 1;\n  } else if (a.length < b.length) {\n    return -1;\n  }\n  return 0;\n};\n\nconst defaultSortFormat = (p0: [string, ...string[]][]) => {\n  const filter = (\n    predicate: (value: [string, ...string[]], index: number) => boolean,\n  ): [string, ...string[]][] => {\n    const notMatchList: [string, ...string[]][] = [];\n    const matchList: [string, ...string[]][] = [];\n    p0.forEach((value, index) => {\n      if (!predicate(value, index)) {\n        notMatchList.push(value);\n      } else {\n        matchList.push(value);\n      }\n    });\n    p0 = notMatchList;\n    return matchList;\n  };\n  return [\n    filter(([k]) => k == 'name'),\n    filter(([k]) => k.startsWith('name:')),\n    filter(([k]) => k == 'namespace'),\n    filter(([k]) => k == 'version'),\n    filter(([k]) => k == 'author'),\n    filter(([k]) => k == 'description'),\n    filter(([k]) => k.startsWith('description:')),\n    filter(([k]) => k == 'license'),\n    filter(([k]) => k == 'copyright'),\n\n    filter(([k]) => k == 'icon'),\n    filter(([k]) => k == 'iconURL'),\n    filter(([k]) => k == 'icon64'),\n    filter(([k]) => k == 'icon64URL'),\n    filter(([k]) => k == 'defaulticon'),\n\n    filter(([k]) => k == 'homepage'),\n    filter(([k]) => k == 'homepageURL'),\n    filter(([k]) => k == 'website'),\n    filter(([k]) => k == 'source'),\n\n    filter(([k]) => k == 'supportURL'),\n    filter(([k]) => k == 'downloadURL'),\n    filter(([k]) => k == 'updateURL'),\n\n    filter(([k]) => k == 'include'),\n    filter(([k]) => k == 'match'),\n    filter(([k]) => k == 'exclude'),\n    filter(([k]) => k == 'exclude-match'),\n    filter(([k]) => k == 'webRequest'),\n\n    filter(([k]) => k == 'require'),\n\n    filter(([k]) => k == 'resource').sort(stringSort),\n\n    filter(([k]) => k == 'sandbox'),\n    filter(([k]) => k == 'tag'),\n\n    filter(([k]) => k == 'connect'),\n\n    filter(([k]) => k == 'grant').sort(stringSort),\n\n    filter(([k]) => k == 'inject-into'),\n    filter(([k]) => k == 'run-at'),\n\n    filter(([k]) => k == 'compatible'),\n    filter(([k]) => k == 'incompatible'),\n    filter(([k]) => k == 'antifeature').sort(stringSort),\n    filter(([k]) => k.startsWith('antifeature:')).sort(stringSort),\n    filter(([k]) => k == 'contributionAmount'),\n    filter(([k]) => k == 'contributionURL'),\n    filter(([k]) => k == 'noframes'),\n    filter(([k]) => k == 'unwrap'),\n    p0,\n  ].flat(1);\n};\n", "import * as acornWalk from 'acorn-walk';\nimport type { OutputChunk, PluginContext } from 'rollup';\nimport { grantNames } from './gmApi';\n\nexport const collectGrant = (\n  context: PluginContext,\n  chunks: OutputChunk[],\n  injectCssCode: string | undefined,\n  minify: boolean,\n): Set<string> => {\n  const codes = new Set<string>();\n  if (injectCssCode) {\n    codes.add(injectCssCode);\n  }\n  for (const chunk of chunks) {\n    if (minify) {\n      // https://github.com/lisonge/vite-plugin-monkey/issues/166\n      const modules = Object.values(chunk.modules);\n      modules.forEach((m) => {\n        const code = m.code;\n        if (code) {\n          codes.add(code);\n        }\n      });\n    }\n    codes.add(chunk.code);\n  }\n  const unusedMembers = new Set<string>(\n    grantNames.filter((s) => s.includes(`.`)),\n  );\n  const endsWithWin = (a: string, b: string): boolean => {\n    if (a.endsWith(b)) {\n      return a === 'monkeyWindow.' + b || a === '_monkeyWindow.' + b;\n    }\n    return false;\n  };\n  const memberHandleMap = Object.fromEntries(\n    grantNames\n      .filter((s) => s.startsWith('window.'))\n      .map((name) => [name, (v: string) => endsWithWin(v, name.split('.')[1])]),\n  );\n  const unusedIdentifiers = new Set<string>(\n    grantNames.filter((s) => !s.includes(`.`)),\n  );\n  const usedGm = new Set<string>();\n  const matchIdentifier = (name: string): boolean => {\n    if (unusedIdentifiers.has(name)) {\n      usedGm.add(name);\n      unusedIdentifiers.delete(name);\n      return true;\n    }\n    return false;\n  };\n  const matchMember = (name: string): boolean => {\n    for (const unusedName of unusedMembers.values()) {\n      if (name.endsWith(unusedName) || memberHandleMap[unusedName]?.(name)) {\n        usedGm.add(unusedName);\n        unusedMembers.delete(unusedName);\n        return true;\n      }\n    }\n    return false;\n  };\n  for (const code of codes) {\n    if (!code.trim()) continue;\n    const ast = context.parse(code);\n    acornWalk.simple(\n      ast,\n      {\n        MemberExpression(node) {\n          if (unusedMembers.size === 0) return;\n          if (\n            node.computed ||\n            node.object.type !== 'Identifier' ||\n            node.property.type !== 'Identifier'\n          ) {\n            return;\n          }\n          if (\n            node.object.name === 'monkeyWindow' ||\n            node.object.name === '_monkeyWindow'\n          ) {\n            if (matchIdentifier(node.property.name)) {\n              return;\n            }\n          }\n          const name = node.object.name + '.' + node.property.name;\n          matchMember(name);\n        },\n        Identifier(node) {\n          // only one layer\n          matchIdentifier(node.name);\n        },\n      },\n      { ...acornWalk.base },\n    );\n    if (unusedMembers.size == 0 && unusedIdentifiers.size == 0) {\n      break;\n    }\n  }\n  return usedGm;\n};\n", "import { resolve } from 'import-meta-resolve';\nimport fs from 'node:fs/promises';\nimport path from 'node:path';\nimport { pathToFileURL } from 'node:url';\nimport type { OutputBundle } from 'rollup';\nimport { transformWithEsbuild } from 'vite';\nimport type { ResolvedMonkeyOption } from './types';\n\nexport const isFirstBoot = (): boolean => {\n  return (Reflect.get(globalThis, '__vite_start_time') ?? 0) < 1000;\n};\n\nexport const compatResolve = (id: string) => {\n  return resolve(id, pathToFileURL(process.cwd() + '/any.js').href);\n};\n\nexport const existFile = async (path: string) => {\n  try {\n    return (await fs.stat(path)).isFile();\n  } catch {\n    return false;\n  }\n};\n\nexport const miniCode = async (code: string, type: 'css' | 'js' = 'js') => {\n  return (\n    await transformWithEsbuild(code, 'any_name.' + type, {\n      minify: true,\n      sourcemap: false,\n      legalComments: 'none',\n    })\n  ).code.trimEnd();\n};\n\nexport const moduleExportExpressionWrapper = (expression: string) => {\n  let n = 0;\n  let identifier = ``;\n  while (expression.includes(identifier)) {\n    identifier = `_${(n || ``).toString(16)}`;\n    n++;\n  }\n  // https://github.com/lisonge/vite-plugin-monkey/issues/70\n  return `(()=>{const ${identifier}=${expression};('default' in ${identifier})||(${identifier}.default=${identifier});return ${identifier}})()`;\n};\n\nexport async function* walk(dirPath: string) {\n  const pathnames = (await fs.readdir(dirPath)).map((s) =>\n    path.join(dirPath, s),\n  );\n  while (pathnames.length > 0) {\n    const pathname = pathnames.pop()!;\n    const state = await fs.lstat(pathname);\n    if (state.isFile()) {\n      yield pathname;\n    } else if (state.isDirectory()) {\n      pathnames.push(\n        ...(await fs.readdir(pathname)).map((s) => path.join(pathname, s)),\n      );\n    }\n  }\n}\n\nexport const getInjectCssCode = async (\n  option: ResolvedMonkeyOption,\n  bundle: OutputBundle,\n) => {\n  const cssTexts: string[] = [];\n  Object.entries(bundle).forEach(([k, v]) => {\n    if (v.type == 'asset' && k.endsWith('.css')) {\n      cssTexts.push(v.source.toString());\n      delete bundle[k];\n    }\n  });\n  const css = cssTexts.join('').trim();\n  if (css) {\n    // use \\x20 to compat unocss, see https://github.com/lisonge/vite-plugin-monkey/issues/45\n    return await option.cssSideEffects(`\\x20` + css + `\\x20`);\n  }\n};\n\nexport const stringifyFunction = <T extends (...args: any[]) => any>(\n  fn: T,\n  ...args: Parameters<T>\n) => {\n  return `;(${fn})(${args.map((v) => JSON.stringify(v)).join(',')});`;\n};\n\nexport const dataJsUrl = (code: string): string => {\n  return 'data:application/javascript,' + encodeURIComponent(code);\n};\n\n/**\n * string -> javascript data url\n * @example\n * dataUrl('console.log(\"hello world\")')\n * // => data:application/javascript,console.log(%22hello%20world%22)\n */\nexport function dataUrl(code: string): string;\n/**\n * function and it parameters -> iife -> mini_iife -> javascript data url\n *\n * @example\n * dataUrl((a)=>{console.log(a)}, 'world')\n * // => data:application/javascript,((z)%3D%3E%7Bconsole.log(a)%7D)('world')\n */\nexport function dataUrl<T extends (...args: any[]) => any>(\n  fn: T,\n  ...args: Parameters<T>\n): Promise<string>;\nexport function dataUrl(p0: any, ...args: any[]): string | Promise<string> {\n  if (typeof p0 == 'string') {\n    return dataJsUrl(p0);\n  }\n  return miniCode(stringifyFunction(p0, ...args)).then(dataJsUrl);\n}\n\nimport { DomUtils, ElementType, parseDocument } from 'htmlparser2';\n\ninterface ScriptResult {\n  src: string;\n  text: string;\n}\n\nexport const parserHtmlScriptResult = (html: string): ScriptResult[] => {\n  const doc = parseDocument(html);\n  type Element =\n    ReturnType<typeof DomUtils.findAll> extends Array<infer T> ? T : never;\n  const scripts = DomUtils.getElementsByTagType(\n    ElementType.Script,\n    doc,\n  ) as Element[];\n  return scripts.map<ScriptResult>((p) => {\n    const src = p.attribs.src ?? '';\n    const textNode = p.firstChild;\n    let text = '';\n    if (textNode?.type == ElementType.Text) {\n      text = textNode.data ?? '';\n    }\n    if (src) {\n      return { src, text };\n    } else {\n      return {\n        src: '',\n        text,\n      };\n    }\n  });\n};\n\nimport crypto from 'node:crypto';\n\nexport const simpleHash = (str = ''): string => {\n  return crypto\n    .createHash('md5')\n    .update(str || '')\n    .digest('base64url')\n    .substring(0, 8);\n};\n\nexport const safeURL = (\n  url: string | URL | undefined | null,\n  base?: string | URL,\n): URL | undefined => {\n  if (!url) return undefined;\n  try {\n    return new URL(url, base);\n  } catch {}\n};\n", "import fs from 'node:fs/promises';\nimport module from 'node:module';\nimport type systemjsPkgT from 'systemjs/package.json';\nimport { dataUrl } from './others';\nimport type { ModuleToUrlFc } from './types';\n\nconst _require = module.createRequire(import.meta.url);\n\nconst systemjsPkg: typeof systemjsPkgT = _require(`systemjs/package.json`);\n\nconst systemjsSubPaths = [\n  'dist/system.min.js',\n  'dist/extras/named-register.min.js',\n];\n\n// https://github.com/systemjs/systemjs/blob/main/docs/api.md#systemconstructor\nconst customSystemInstanceCode = `;(typeof System!='undefined')&&(System=new System.constructor());`;\n\nconst systemjsAbsolutePaths = systemjsSubPaths.map((s) => {\n  return _require.resolve(`systemjs/` + s);\n});\n\nexport const getSystemjsTexts = async (): Promise<string[]> => {\n  return Promise.all(\n    systemjsAbsolutePaths\n      .map((s) =>\n        fs.readFile(s, 'utf-8').then((s) =>\n          s\n            .trim()\n            .replace(/^\\/\\*[\\s\\S]*?\\*\\//, '')\n            .replace(/\\/\\/.*map$/, '')\n            .trim(),\n        ),\n      )\n      .concat([Promise.resolve(customSystemInstanceCode)]),\n  );\n};\n\nexport const getSystemjsRequireUrls = (fn: ModuleToUrlFc) => {\n  return systemjsSubPaths\n    .map((p) => {\n      return fn(systemjsPkg.version, systemjsPkg.name, p, p);\n    })\n    .concat([dataUrl(customSystemInstanceCode)]);\n};\n", "import type * as acorn from 'acorn';\nimport * as acornWalk from 'acorn-walk';\nimport MagicString from 'magic-string';\nimport type {\n  OutputAsset,\n  OutputBundle,\n  OutputChunk,\n  PluginContext,\n} from 'rollup';\n\ninterface AwaitCallExpression extends acorn.CallExpression {\n  callee: acorn.Identifier;\n}\n\nconst awaitOffset = `await`.length;\nconst initTlaIdentifier = `_TLA_`;\n\nexport const getSafeTlaIdentifier = (rawBundle: OutputBundle) => {\n  const codes: string[] = [];\n  for (const chunk of Object.values(rawBundle)) {\n    if (chunk.type == 'chunk') {\n      codes.push(chunk.code);\n    }\n  }\n  let x = 0;\n  let identifier = initTlaIdentifier;\n  while (codes.some((code) => code.includes(identifier))) {\n    x++;\n    identifier = initTlaIdentifier + x.toString(36);\n  }\n  return identifier;\n};\n\nconst startWith = (\n  text: string,\n  searchString: string,\n  position = 0,\n  ignoreString: string,\n) => {\n  for (let i = position; i < text.length; i++) {\n    if (ignoreString.includes(text[i])) {\n      continue;\n    }\n    return text.startsWith(searchString, i);\n  }\n  return false;\n};\n\nconst includes = (\n  str: string,\n  start: number,\n  end: number,\n  substr: string,\n): boolean => {\n  const i = str.indexOf(substr, start);\n  return i >= 0 && i + substr.length < end;\n};\n\nexport const transformTlaToIdentifier = (\n  context: PluginContext,\n  chunk: OutputAsset | OutputChunk,\n  identifier: string,\n) => {\n  if (chunk.type == 'chunk') {\n    const code = chunk.code;\n    if (!code.includes(`await`)) {\n      return;\n    }\n    const ast = context.parse(code);\n    const tlaNodes: acorn.AwaitExpression[] = [];\n    const tlaForOfNodes: acorn.ForOfStatement[] = [];\n    acornWalk.simple(\n      ast,\n      {\n        AwaitExpression(node) {\n          // top level await\n          tlaNodes.push(node);\n        },\n        ForOfStatement(node) {\n          if (node.await === true) {\n            tlaForOfNodes.push(node);\n          }\n        },\n      },\n      { ...acornWalk.base, Function: () => {} },\n    );\n    if (tlaNodes.length > 0 || tlaForOfNodes.length > 0) {\n      const ms = new MagicString(code);\n      tlaNodes.forEach((node) => {\n        if (\n          !startWith(chunk.code, '(', node.start + awaitOffset, '\\x20\\t\\r\\n')\n        ) {\n          // await xxx -> await (xxx)\n          ms.appendLeft(node.start + awaitOffset, `(`);\n          ms.appendRight(node.end, `)`);\n        }\n\n        // await (xxx) -> __topLevelAwait__ (xxx)\n        ms.update(node.start, node.start + awaitOffset, identifier);\n      });\n      tlaForOfNodes.forEach((node) => {\n        // for await(const x of xxx){} -> __topLevelAwait_FOR ((async()=>{ /*start*/for await(const x of xxx){}/*end*/  })());\n        ms.appendLeft(node.start, `${identifier + `FOR`}((async()=>{`);\n        ms.appendRight(node.end, `})());`);\n      });\n      return {\n        code: ms.toString(),\n        map: ms.generateMap(),\n      };\n    }\n  }\n};\n\nexport const transformIdentifierToTla = (\n  context: PluginContext,\n  chunk: OutputAsset | OutputChunk,\n  identifier: string,\n) => {\n  if (chunk.type == 'chunk') {\n    if (!chunk.code.includes(identifier)) {\n      return;\n    }\n\n    const forIdentifier = identifier + `FOR`;\n\n    const ast = context.parse(chunk.code);\n    const tlaCallNodes: AwaitCallExpression[] = [];\n    const forTlaCallNodes: AwaitCallExpression[] = [];\n    const topFnNodes: acorn.Function[] = [];\n    acornWalk.simple(\n      ast,\n      {\n        CallExpression(node) {\n          if ('name' in node.callee) {\n            const { name, type } = node.callee;\n            if (type === `Identifier`) {\n              if (name === identifier) {\n                // top level await\n                tlaCallNodes.push({ ...node, callee: node.callee });\n              } else if (name === forIdentifier) {\n                // top level for await\n                forTlaCallNodes.push({ ...node, callee: node.callee });\n              }\n            }\n          }\n        },\n      },\n      {\n        ...acornWalk.base,\n        Function: (node, state, callback) => {\n          if (topFnNodes.length == 0) {\n            topFnNodes.push(node);\n          }\n          if (includes(chunk.code, node.start, node.end, identifier)) {\n            return acornWalk.base.Function?.(node, state, callback);\n          }\n        },\n      },\n    );\n    if (tlaCallNodes.length > 0 || forTlaCallNodes.length > 0) {\n      const ms = new MagicString(chunk.code, {});\n      tlaCallNodes.forEach((node) => {\n        const callee = node.callee;\n        // __topLevelAwait__ (xxx) -> await (xxx)\n        ms.update(callee.start, callee.end, 'await');\n      });\n      forTlaCallNodes.forEach((node) => {\n        // __topLevelAwait_FOR ((async()=>{ /*start*/for await(const x of xxx){}/*end*/  })()); -> for await(const x of xxx){}\n        // @ts-ignore\n        const forOfNode = node.arguments?.[0]?.callee?.body\n          ?.body?.[0] as acorn.ForOfStatement;\n        ms.update(node.start, forOfNode.start, '');\n        ms.update(forOfNode.end, node.end, '');\n      });\n      topFnNodes.forEach((node) => {\n        ms.appendLeft(node.start, `async\\x20`);\n      });\n      chunk.code = ms.toString();\n    }\n  }\n};\n", "import type { Plugin } from 'vite';\nimport type { ResolvedMonkeyOption } from '../utils/types';\n\nexport const configFactory = (\n  getOption: () => Promise<ResolvedMonkeyOption>,\n): Plugin => {\n  let option: ResolvedMonkeyOption;\n  return {\n    name: 'monkey:config',\n    async config(userConfig) {\n      option = await getOption();\n      return {\n        resolve: {\n          alias: {\n            [option.clientAlias]: 'vite-plugin-monkey/dist/client',\n          },\n        },\n        esbuild: {\n          supported: {\n            'top-level-await': true,\n          },\n        },\n        build: {\n          assetsInlineLimit: Number.MAX_SAFE_INTEGER,\n          chunkSizeWarningLimit: Number.MAX_SAFE_INTEGER,\n          modulePreload: false,\n          assetsDir: './',\n          cssCodeSplit: false,\n          minify: userConfig.build?.minify ?? false,\n          cssMinify: userConfig.build?.cssMinify ?? true,\n          sourcemap: false,\n          rollupOptions: {\n            input: option.entry,\n          },\n        },\n      };\n    },\n  };\n};\n", "import { normalizePath } from 'vite';\nimport type { ResolvedMonkeyOption } from '../utils/types';\nimport { getModuleRealInfo } from '../utils/pkg';\nimport type { Plugin } from 'vite';\n\nexport const externalGlobalsFactory = (\n  getOption: () => Promise<ResolvedMonkeyOption>,\n): Plugin => {\n  let option: ResolvedMonkeyOption;\n  return {\n    name: 'monkey:externalGlobals',\n    enforce: 'pre',\n    apply: 'build',\n    async config() {\n      option = await getOption();\n      for (const [moduleName, varName2LibUrl] of option.build.externalGlobals) {\n        const { name, version } = await getModuleRealInfo(moduleName);\n        if (typeof varName2LibUrl == 'string') {\n          option.globalsPkg2VarName[moduleName] = varName2LibUrl;\n        } else if (typeof varName2LibUrl == 'function') {\n          option.globalsPkg2VarName[moduleName] = await varName2LibUrl(\n            version,\n            name,\n            moduleName,\n          );\n        } else if (varName2LibUrl instanceof Array) {\n          const [varName, ...libUrlList] = varName2LibUrl;\n          if (typeof varName == 'string') {\n            option.globalsPkg2VarName[moduleName] = varName;\n          } else if (typeof varName == 'function') {\n            option.globalsPkg2VarName[moduleName] = await varName(\n              version,\n              name,\n              moduleName,\n            );\n          }\n          for (const libUrl of libUrlList) {\n            // keep add order\n            if (typeof libUrl == 'string') {\n              option.requirePkgList.push({ url: libUrl, moduleName });\n            } else if (typeof libUrl == 'function') {\n              option.requirePkgList.push({\n                url: await libUrl(version, name, moduleName),\n                moduleName,\n              });\n            }\n          }\n        }\n      }\n      return {\n        build: {\n          rollupOptions: {\n            external(source, _importer, _isResolved) {\n              return source in option.globalsPkg2VarName;\n            },\n          },\n        },\n      };\n    },\n    async generateBundle() {\n      const usedModIdSet = new Set(\n        Array.from(this.getModuleIds()).map((s) => normalizePath(s)),\n      );\n      option.collectRequireUrls = option.requirePkgList\n        .filter((p) => usedModIdSet.has(p.moduleName))\n        .map((p) => p.url);\n    },\n  };\n};\n", "import fs from 'node:fs/promises';\nimport path from 'node:path';\nimport { normalizePath } from 'vite';\nimport { compatResolve, existFile } from './others';\n\ninterface RawPackageJson {\n  name?: string;\n  version?: string;\n  description?: string;\n  license?: string;\n  author?: string | { name: string };\n  homepage?: string;\n  repository?: string | { url?: string };\n  bugs?: string | { url?: string };\n}\n\ninterface PackageJson {\n  name?: string;\n  version?: string;\n  description?: string;\n  license?: string;\n  author?: string;\n  homepage?: string;\n  repository?: string;\n  bugs?: string;\n}\n\nexport const getProjectPkg = async (): Promise<PackageJson> => {\n  const rawPkg: RawPackageJson | undefined = await fs\n    .readFile(path.resolve(process.cwd(), 'package.json'), 'utf-8')\n    .then(JSON.parse)\n    .catch(() => {});\n\n  const pkg: PackageJson = {};\n  if (!rawPkg) return pkg;\n  Object.entries(rawPkg).forEach(([k, v]) => {\n    if (typeof v == 'string') {\n      Reflect.set(pkg, k, v);\n    }\n  });\n  if (\n    typeof rawPkg.author === 'object' &&\n    typeof rawPkg.author?.name == 'string'\n  ) {\n    pkg.author = rawPkg.author.name;\n  }\n  if (typeof rawPkg.bugs === 'object' && typeof rawPkg.bugs?.url == 'string') {\n    pkg.bugs = rawPkg.bugs.url;\n  }\n  if (\n    typeof rawPkg.repository === 'object' &&\n    typeof rawPkg.repository?.url == 'string'\n  ) {\n    const { url } = rawPkg.repository;\n    if (url.startsWith('http')) {\n      pkg.repository = url;\n    } else if (url.startsWith('git+http')) {\n      pkg.repository = url.substring(4);\n    }\n  }\n  return pkg;\n};\n\nconst isScopePkg = (name: string): boolean => name.startsWith('@');\nconst resolveModuleFromPath = async (\n  subpath: string,\n): Promise<string | undefined> => {\n  const p = normalizePath(process.cwd()).split('/');\n  for (let i = p.length; i > 0; i--) {\n    const p2 = `${p.slice(0, i).join('/')}/node_modules/${subpath}`;\n    if (await existFile(p2)) {\n      return p2;\n    }\n  }\n};\n\nconst compatResolveModulePath = async (id: string): Promise<string> => {\n  try {\n    return compatResolve(id);\n  } catch (e) {\n    // not defined in pkg/package.json but exist in pkg/subpath\n    // https://github.com/lisonge/vite-plugin-monkey/issues/169\n    const r = await resolveModuleFromPath(id);\n    if (!r) {\n      throw e;\n    }\n    return r;\n  }\n};\n\nexport const getModuleRealInfo = async (importName: string) => {\n  const nameNoQuery = normalizePath(importName.split('?')[0]);\n  const resolveName = await (async () => {\n    const n = normalizePath(await compatResolveModulePath(nameNoQuery)).replace(\n      /.*\\/node_modules\\/[^/]+\\//,\n      '',\n    );\n    if (isScopePkg(importName)) {\n      return n.split('/').slice(1).join('/');\n    }\n    return n;\n  })();\n  let version: string | undefined = undefined;\n  const nameList = nameNoQuery.split('/');\n  let name = nameNoQuery;\n  while (nameList.length > 0) {\n    name = nameList.join('/');\n    const filePath = await (async () => {\n      const p = await resolveModuleFromPath(`${name}/package.json`);\n      if (p) {\n        return p;\n      }\n      try {\n        return compatResolve(`${name}/package.json`);\n      } catch {\n        return undefined;\n      }\n    })();\n    if (filePath === undefined || !(await existFile(filePath))) {\n      nameList.pop();\n      continue;\n    }\n    const modulePack: PackageJson = JSON.parse(\n      await fs.readFile(filePath, 'utf-8'),\n    );\n    version = modulePack.version;\n    break;\n  }\n  if (version === undefined) {\n    console.warn(\n      `[plugin-monkey] not found module ${nameNoQuery} version, use ${nameNoQuery}@latest`,\n    );\n    name = nameNoQuery;\n    version = 'latest';\n  }\n  return { version, name, resolveName };\n};\n", "import type { Plugin } from 'vite';\nimport { miniCode } from '../utils/others';\n\nconst cssLoader = (name: string) => {\n  // @ts-ignore\n  const css = GM_getResourceText(name);\n  // @ts-ignore\n  GM_addStyle(css);\n  return css;\n};\n\nconst jsonLoader = (name: string): unknown =>\n  // @ts-ignore\n  JSON.parse(GM_getResourceText(name));\n\nconst urlLoader = (name: string, type: string) =>\n  // @ts-ignore\n  GM_getResourceURL(name, false).replace(\n    /^data:application;base64,/,\n    `data:${type};base64,`,\n  );\n\nconst rawLoader = (name: string) =>\n  // @ts-ignore\n  GM_getResourceText(name);\n\nconst loaderCode = [\n  `export const cssLoader = ${cssLoader}`,\n  `export const jsonLoader = ${jsonLoader}`,\n  `export const urlLoader = ${urlLoader}`,\n  `export const rawLoader = ${rawLoader}`,\n].join(';');\n\nexport const externalLoaderFactory = (): Plugin => {\n  return {\n    name: 'monkey:externalLoader',\n    apply: 'build',\n    async resolveId(id) {\n      if (id == 'virtual:plugin-monkey-loader') {\n        return '\\0' + id;\n      }\n    },\n    async load(id) {\n      if (id == '\\0virtual:plugin-monkey-loader') {\n        return miniCode(loaderCode, 'js');\n      }\n    },\n  };\n};\n", "import { normalizePath } from 'vite';\nimport { miniCode } from '../utils/others';\nimport { getModuleRealInfo } from '../utils/pkg';\nimport type { PkgOptions, ResolvedMonkeyOption } from '../utils/types';\nimport type { Plugin, ResolvedConfig } from 'vite';\n\nconst resourceImportPrefix = '\\0monkey-resource-import:';\n\nexport const externalResourcePlugin = (\n  getOption: () => Promise<ResolvedMonkeyOption>,\n): Plugin => {\n  let option: ResolvedMonkeyOption;\n  let viteConfig: ResolvedConfig;\n  let mrmime: typeof import('mrmime');\n  const resourceRecord: Record<\n    string,\n    { resourceName: string; resourceUrl: string }\n  > = {};\n  return {\n    name: 'monkey:externalResource',\n    apply: 'build',\n    async config() {\n      option = await getOption();\n      mrmime = await import('mrmime');\n    },\n    configResolved(config) {\n      viteConfig = config;\n    },\n    async resolveId(id) {\n      const { externalResource } = option.build;\n      if (id in externalResource) {\n        return resourceImportPrefix + id + '\\0';\n      }\n      // see https://github.com/vitejs/vite/blob/5d56e421625b408879672a1dd4e774bae3df674f/packages/vite/src/node/plugins/css.ts#L431-L434\n      const [resource, query] = id.split('?', 2);\n      if (resource.endsWith('.css') && query) {\n        const id2 = [\n          resource,\n          '?',\n          query\n            .split('&')\n            .filter((e) => e != 'used')\n            .join(`&`),\n        ].join('');\n        if (id2 in externalResource) {\n          return resourceImportPrefix + id2 + '\\0';\n        }\n      }\n    },\n    async load(id) {\n      if (id.startsWith(resourceImportPrefix) && id.endsWith('\\0')) {\n        const { externalResource } = option.build;\n        const importName = id.substring(\n          resourceImportPrefix.length,\n          id.length - 1,\n        );\n        if (!(importName in externalResource)) {\n          return;\n        }\n        const pkg = await getModuleRealInfo(importName);\n        const {\n          resourceName: resourceNameFn,\n          resourceUrl: resourceUrlFn,\n          loader,\n          nodeLoader,\n        } = externalResource[importName];\n        const resourceName = await resourceNameFn({ ...pkg, importName });\n        const resourceUrl = await resourceUrlFn({ ...pkg, importName });\n        resourceRecord[importName] = {\n          resourceName,\n          resourceUrl,\n        };\n\n        if (nodeLoader) {\n          return miniCode(\n            await nodeLoader({\n              ...pkg,\n              resourceName,\n              resourceUrl,\n              importName,\n            }),\n          );\n        } else if (loader) {\n          let fnText: string;\n          if (\n            loader.prototype && // not arrow function\n            loader.name.length > 0 &&\n            loader.name != 'function' // not anonymous function\n          ) {\n            if (Reflect.get(loader, Symbol.toStringTag) == 'AsyncFunction') {\n              fnText = loader\n                .toString()\n                .replace(/^[\\s\\S]+?\\(/, 'async function(');\n            } else {\n              fnText = loader.toString().replace(/^[\\s\\S]+?\\(/, 'function(');\n            }\n          } else {\n            fnText = loader.toString();\n          }\n          return miniCode(\n            `export default (${fnText})(${JSON.stringify({\n              resourceUrl,\n              importName,\n              ...pkg,\n            } as PkgOptions)})`,\n          );\n        }\n\n        let moduleCode: string | undefined = undefined;\n        const [resource, query] = importName.split('?', 2);\n        const ext = resource.split('.').pop()!;\n        const mimeType = mrmime.lookup(ext) ?? 'application/octet-stream';\n        const suffixSet = new URLSearchParams(query);\n        if (suffixSet.has('url') || suffixSet.has('inline')) {\n          moduleCode = [\n            `import {urlLoader as loader} from 'virtual:plugin-monkey-loader'`,\n            `export default loader(...${JSON.stringify([\n              resourceName,\n              mimeType,\n            ])})`,\n          ].join(';');\n        } else if (suffixSet.has('raw')) {\n          moduleCode = [\n            `import {rawLoader as loader} from 'virtual:plugin-monkey-loader'`,\n            `export default loader(...${JSON.stringify([resourceName])})`,\n          ].join(';');\n        } else if (ext == 'json') {\n          // export name will bring side effect\n          moduleCode = [\n            `import {jsonLoader as loader} from 'virtual:plugin-monkey-loader'`,\n            `export default loader(...${JSON.stringify([resourceName])})`,\n          ].join(';');\n        } else if (ext == 'css') {\n          moduleCode = [\n            `import {cssLoader as loader} from 'virtual:plugin-monkey-loader'`,\n            `export default loader(...${JSON.stringify([resourceName])})`,\n          ].join(';');\n        } else if (viteConfig.assetsInclude(importName.split('?', 1)[0])) {\n          const mediaType = mrmime.mimes[ext];\n          moduleCode = [\n            `import {urlLoader as loader} from 'virtual:plugin-monkey-loader'`,\n            `export default loader(...${JSON.stringify([\n              resourceName,\n              mediaType,\n            ])})`,\n          ].join(';');\n        }\n        if (moduleCode) {\n          if (\n            moduleCode.includes('rawLoader') ||\n            moduleCode.includes('jsonLoader') ||\n            moduleCode.includes('cssLoader')\n          ) {\n            option.userscript.grant.add('GM_getResourceText');\n          } else if (moduleCode.includes('urlLoader')) {\n            option.userscript.grant.add('GM_getResourceURL');\n          }\n          return miniCode(moduleCode);\n        }\n\n        throw new Error(`module: ${importName} not found loader`);\n      }\n    },\n    generateBundle() {\n      const usedModIdSet = new Set(\n        Array.from(this.getModuleIds()).map((s) => normalizePath(s)),\n      );\n      Array.from(usedModIdSet).forEach((id) => {\n        if (id.startsWith(resourceImportPrefix) && id.endsWith('\\0')) {\n          usedModIdSet.add(\n            id.substring(resourceImportPrefix.length, id.length - 1),\n          );\n        }\n      });\n      const collectResource: Record<string, string> = {};\n      Object.entries(resourceRecord).forEach(\n        ([importName, { resourceName, resourceUrl }]) => {\n          if (usedModIdSet.has(importName)) {\n            collectResource[resourceName] = resourceUrl;\n          }\n        },\n      );\n      option.collectResource = collectResource;\n    },\n  };\n};\n", "import type { Plugin, ResolvedConfig } from 'vite';\n\n/**\n * convert `export default \"/src/assets/a.png\"` to `export default new URL(\"/src/assets/a.png\", import.meta['url']).href`\n */\nexport const fixAssetUrlFactory = (): Plugin => {\n  let viteConfig: ResolvedConfig;\n  return {\n    name: 'monkey:fixAssetUrl',\n    apply: 'serve',\n    async configResolved(resolvedConfig) {\n      viteConfig = resolvedConfig;\n    },\n    async transform(code, id) {\n      const [_, query = 'url'] = id.split('?', 2);\n      if (\n        (query.split('&').includes('url') || viteConfig.assetsInclude(id)) &&\n        code.match(/^\\s*export\\s+default/)\n      ) {\n        const ast = this.parse(code);\n        const defaultNode = ast.body[0];\n        if (defaultNode?.type == 'ExportDefaultDeclaration') {\n          const childNode = defaultNode?.declaration;\n          if (\n            childNode?.type == 'Literal' &&\n            typeof childNode.value == 'string' &&\n            childNode.value[0] === '/'\n          ) {\n            const p0 = JSON.stringify(childNode.value);\n            return `export default new URL(${p0}, import.meta['url']).href`;\n          }\n        }\n      }\n    },\n  };\n};\n", "import type { Plugin } from 'vite';\n\nexport const fixClientFactory = (): Plugin => {\n  return {\n    name: 'monkey:fixClient',\n    apply: 'serve',\n    async transform(code, id) {\n      if (id.endsWith('node_modules/vite/dist/client/client.mjs')) {\n        // https://github.com/vitejs/vite/blob/main/packages/vite/src/client/client.ts\n        // https://cdn.jsdelivr.net/npm/vite@latest/dist/client/client.mjs\n        return code.replaceAll(\n          '__BASE__',\n          `new URL(__BASE__ || '/', import.meta['url']).href`,\n        );\n      }\n    },\n  };\n};\n", "import type { Plugin } from 'vite';\n\nexport const fixCssUrlFactory = (): Plugin => {\n  // https://github.com/lisonge/vite-plugin-monkey/issues/214\n  return {\n    name: 'monkey:fixCssUrl',\n    apply: 'serve',\n    async config() {\n      const postUrl = (await import('postcss-url')).default;\n      return {\n        css: {\n          postcss: {\n            plugins: [postUrl({ url: 'inline' })],\n          },\n        },\n      };\n    },\n  };\n};\n", "import path from 'node:path';\nimport type { Plugin, ResolvedConfig } from 'vite';\nimport { normalizePath } from 'vite';\nimport { walk } from '../utils/others';\nimport { fcToHtml, previewTemplate } from '../utils/template';\n\nexport const perviewFactory = (): Plugin => {\n  let viteConfig: ResolvedConfig;\n  return {\n    name: 'monkey:perview',\n    apply: 'serve',\n    configResolved(config) {\n      viteConfig = config;\n    },\n    async configurePreviewServer(server) {\n      server.middlewares.use(async (req, res, next) => {\n        if (['/', '/index.html'].includes((req.url ?? '').split('?')[0])) {\n          const distDirPath = path.join(process.cwd(), viteConfig.build.outDir);\n          const urls: string[] = [];\n          for await (const pathname of walk(distDirPath)) {\n            if (pathname.endsWith('.user.js')) {\n              const fileName = normalizePath(\n                path.relative(distDirPath, pathname),\n              );\n              urls.push(`/` + fileName);\n            }\n          }\n          res.setHeader('content-type', 'text/html; charset=utf-8');\n          res.end(fcToHtml(previewTemplate, urls));\n          return;\n        }\n        next();\n      });\n    },\n  };\n};\n", "import { stringifyFunction } from './others';\n\nconst htmlText = /* html */ `\n<!DOCTYPE html>\n<html>\n  <head>\n    <meta charset=\"UTF-8\" />\n    <link rel=\"icon\" type=\"image/svg+xml\" href=\"https://vite.dev/logo.svg\" />\n    <title>Vite</title>\n  </head>\n  <script type=\"module\" data-source=\"vite-plugin-monkey\">\n  __CODE__\n  </script>\n</html>\n`.trimStart();\n\nexport const fcToHtml = <T extends (...args: any[]) => any>(\n  fn: T,\n  ...args: Parameters<T>\n) => {\n  return htmlText.replace(`__CODE__`, stringifyFunction(fn, ...args));\n};\n\nexport const serverInjectFn = (entrySrc: string) => {\n  /// https://github.com/Tampermonkey/tampermonkey/issues/1567\n  // @ts-ignore\n  // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n  window.GM; // must exist, see https://github.com/Tampermonkey/tampermonkey/issues/1567\n\n  const key = `__monkeyWindow-` + new URL(entrySrc).origin;\n  // @ts-ignore\n  document[key] = window;\n  console.log(`[vite-plugin-monkey] mount monkeyWindow to document`);\n  // @ts-ignore\n  if (typeof GM_addElement === 'function') {\n    // @ts-ignore\n    GM_addElement(document.head, 'script', {\n      type: 'module',\n      src: entrySrc,\n    });\n  } else {\n    const script = document.createElement('script');\n    script.type = 'module';\n    // @ts-ignore\n    if (window.trustedTypes) {\n      // https://github.com/lisonge/vite-plugin-monkey/issues/205\n      // @ts-ignore\n      const policy = window.trustedTypes.createPolicy(key, {\n        createScriptURL: (input: unknown) => input,\n      });\n      const trustedScriptURL = policy.createScriptURL(entrySrc);\n      script.src = trustedScriptURL;\n    } else {\n      script.src = entrySrc;\n    }\n    document.head.append(script);\n  }\n  console.log(`[vite-plugin-monkey] mount entry module to document.head`);\n};\n\nexport const mountGmApiFn = (meta: ImportMeta, apiNames: string[] = []) => {\n  const key = `__monkeyWindow-` + new URL(meta.url).origin;\n  // @ts-ignore\n  const monkeyWindow: Window = document[key];\n  if (!monkeyWindow) {\n    console.log(`[vite-plugin-monkey] not found monkeyWindow`);\n    return;\n  }\n\n  // @ts-ignore\n  window.unsafeWindow = window;\n  console.log(`[vite-plugin-monkey] mount unsafeWindow to unsafeWindow`);\n\n  apiNames.push('GM');\n  let mountedApiSize = 0;\n  apiNames.forEach((apiName) => {\n    // @ts-ignore\n    const fn = monkeyWindow[apiName];\n    if (fn) {\n      // @ts-ignore\n      window[apiName] = monkeyWindow[apiName];\n      mountedApiSize++;\n    }\n  });\n  console.log(\n    `[vite-plugin-monkey] mount ${mountedApiSize}/${apiNames.length} GM api to unsafeWindow`,\n  );\n};\n\nexport const virtualHtmlTemplate = async (url: string) => {\n  const delay = (n = 0) => new Promise<void>((res) => setTimeout(res, n));\n  await delay();\n  const u = new URL(url, location.origin);\n  u.searchParams.set('origin', u.origin);\n  if (window == window.parent) {\n    location.href = u.href;\n    await delay(500);\n    window.close();\n    return;\n  }\n  // if in iframe, like codesandbox\n  const style = document.createElement('style');\n  document.head.append(style);\n  style.innerText = /* css */ `\n  body {\n    font-family: Arial, sans-serif;\n    margin: 0;\n  }\n  .App {\n    margin: 25px;\n  }\n  p {\n    font-size: 1.5em;\n  }\n  a {\n    color: blue;\n    text-decoration: none;\n    font-size: 1.5em;\n  }\n  a:hover {\n    text-decoration: underline;\n  }\n`.trim();\n  document.body.innerHTML = /* html */ `\n  <div class=\"App\">\n    <h1>PREVIEW PAGE</h1>\n    <p>Click the links below to install userscripts:</p>\n    <a target=\"_blank\"></a></th>\n  </div>\n  `.trim();\n  await delay();\n  const a = document.querySelector('a')!;\n  a.href = location.href;\n  a.text = location.href;\n};\n\nexport const previewTemplate = async (urls: string[]) => {\n  const delay = (n = 0) => new Promise<void>((res) => setTimeout(res, n));\n  await delay();\n  const style = document.createElement('style');\n  document.head.append(style);\n  style.innerText = /* css */ `\n  body {\n    font-family: Arial, sans-serif;\n    margin: 0;\n  }\n  .App {\n    margin: 25px;\n  }\n  p {\n    font-size: 1.5em;\n  }\n  table {\n    width: 100%;\n    border-collapse: collapse;\n    font-size: 1.5em;\n  }\n  th, td {\n    border: 1px solid black;\n    padding: 8px;\n    text-align: left;\n  }\n  th {\n    background-color: #f2f2f2;\n  }\n  a {\n    color: blue;\n    text-decoration: none;\n  }\n  a:hover {\n    text-decoration: underline;\n  }\n`.trim();\n  if (window == window.parent && urls.length == 1) {\n    const u = new URL(urls[0], location.origin);\n    location.href = u.href;\n    await delay(500);\n    window.close();\n    return;\n  } else if (urls.length == 0) {\n    document.body.innerHTML = /* html */ `\n    <div class=\"App\">\n      <h1> There is no script to install </h1>\n    </div>\n    `.trim();\n    return;\n  } else {\n    document.body.innerHTML = /* html */ `\n    <div class=\"App\">\n      <h1>PREVIEW PAGE</h1>\n      <p>Click the links below to install userscripts:</p>\n      <table>\n        <tr>\n          <th>No.</th>\n          <th>Install Link</th>\n        </tr>\n      </table>\n    </div>\n    `.trim();\n    await delay();\n    const table = document.querySelector<HTMLElement>(`table`)!;\n    urls.sort().forEach((u, index) => {\n      const tr = document.createElement('tr');\n      const td1 = document.createElement('td');\n      const td2 = document.createElement('td');\n      const a = document.createElement('a');\n      td1.innerText = `${index + 1}`;\n      if (window != window.parent) {\n        a.target = '_blank';\n      }\n      a.href = u;\n      a.textContent = new URL(u, location.origin).href;\n      td2.append(a);\n      tr.append(td1);\n      tr.append(td2);\n      table.append(tr);\n    });\n  }\n};\n", "import type { Plugin } from 'vite';\nimport { gmIdentifiers } from '../utils/gmApi';\n\n// https://github.com/Tampermonkey/tampermonkey/issues/1567\nconst clientSourceId = 'vite-plugin-monkey/dist/client';\nconst clientId = '\\0' + clientSourceId;\nexport const redirectClientFactory = (): Plugin => {\n  return {\n    name: 'monkey:redirectClient',\n    enforce: 'pre',\n    apply: 'build',\n    resolveId(source) {\n      if (source === clientSourceId) {\n        return clientId;\n      }\n    },\n    load(id) {\n      if (id == clientId) {\n        const identifiers = ['GM', ...gmIdentifiers, 'unsafeWindow'];\n        // https://github.com/evanw/esbuild/issues/2267#issuecomment-1138445856\n        const declarations = identifiers\n          .map((v) => {\n            return `var _${v} = /* @__PURE__ */ (() => typeof ${v} != \"undefined\" ? ${v} : undefined)();`;\n          })\n          .concat('var _monkeyWindow = /* @__PURE__ */ (() => window)();');\n        const exportIdentifiers = identifiers.concat('monkeyWindow');\n        return (\n          declarations.join('\\n') +\n          `\\nexport {${exportIdentifiers\n            .map((v) => `  _${v} as ${v},`)\n            .join('\\n')}};`\n        );\n      }\n    },\n  };\n};\n", "import fs from 'node:fs/promises';\nimport type { ServerResponse } from 'node:http';\nimport path from 'node:path';\nimport type { Plugin, ResolvedConfig } from 'vite';\nimport { normalizePath } from 'vite';\nimport { finalMonkeyOptionToComment } from '../userscript';\nimport { gmIdentifiers } from '../utils/gmApi';\nimport { openBrowser } from '../utils/openBrowser';\nimport {\n  existFile,\n  isFirstBoot,\n  parserHtmlScriptResult,\n  safeURL,\n  simpleHash,\n  stringifyFunction,\n} from '../utils/others';\nimport { mountGmApiFn, serverInjectFn } from '../utils/template';\nimport type { ResolvedMonkeyOption } from '../utils/types';\n\nconst urlPrefix = '/__vite-plugin-monkey.';\nexport const installUserPath = urlPrefix + 'install.user.js';\nconst gmApiPath = urlPrefix + 'gm.api.js';\nconst entryPath = urlPrefix + 'entry.js';\nconst pullPath = urlPrefix + 'pull.js';\n\nconst restartStoreKey = '__vite_plugin_monkey_install_url';\nconst localHost = '127.0.0.1';\nconst localOrigin = `http://${localHost}`;\nconst htmlPlaceholder = '<html><head></head><body></body></html>';\n\nexport const serverFactory = (\n  getOption: () => Promise<ResolvedMonkeyOption>,\n): Plugin => {\n  let option: ResolvedMonkeyOption;\n  let viteConfig: ResolvedConfig;\n  return {\n    name: 'monkey:server',\n    apply: 'serve',\n    async config(userConfig) {\n      option = await getOption();\n      return {\n        preview: {\n          host: userConfig.preview?.host ?? localHost,\n          cors: true,\n        },\n        server: {\n          host: userConfig.server?.host ?? localHost,\n          open: userConfig.server?.open ?? option.server.open,\n          cors: true,\n        },\n      };\n    },\n    async configResolved(resolvedConfig) {\n      viteConfig = resolvedConfig;\n    },\n    async configureServer(server) {\n      for (const [k, v] of Object.entries(option.userscript.name)) {\n        Reflect.set(option.userscript.name, k, option.server.prefix(v));\n      }\n\n      // support dev env\n      option.userscript.grant.add('*');\n\n      // https://github.com/lisonge/vite-plugin-monkey/issues/205\n      server.middlewares.use((_, res, next) => {\n        const name = 'access-control-allow-private-network';\n        if (!res.hasHeader(name)) {\n          res.setHeader(name, 'true');\n        }\n        next();\n      });\n\n      const setScriptHeader = (res: ServerResponse) => {\n        res.setHeader('access-control-allow-origin', '*');\n        res.setHeader('content-type', 'application/javascript');\n      };\n\n      server.middlewares.use(async (req, res, next) => {\n        if (!(req.method === 'GET' && req.url && req.url.startsWith(urlPrefix)))\n          return next();\n        const url = new URL(req.url, localOrigin);\n        if (url.pathname === installUserPath) {\n          setScriptHeader(res);\n          const origin =\n            safeURL(url.searchParams.get('origin'))?.origin ||\n            (() => {\n              const host = ((h) => {\n                return typeof h === 'string'\n                  ? h !== '0.0.0.0'\n                    ? h\n                    : localHost\n                  : localHost;\n              })(viteConfig.server.host);\n              return `${viteConfig.server.https ? 'https' : 'http'}://${host}:${viteConfig.server.port}`;\n            })();\n          Reflect.set(globalThis, restartStoreKey, origin);\n          res.end(\n            [\n              await finalMonkeyOptionToComment(option, new Set(), 'serve'),\n              stringifyFunction(\n                serverInjectFn,\n                new URL(entryPath, origin).href,\n              ),\n              '',\n            ].join('\\n\\n'),\n          );\n        } else if (url.pathname === entryPath) {\n          setScriptHeader(res);\n          const results = await server\n            .transformIndexHtml('/', htmlPlaceholder, req.originalUrl)\n            .then(parserHtmlScriptResult);\n          const entryUrls: string[] = (\n            option.server.mountGmApi ? [gmApiPath] : []\n          ).concat(\n            results.map((v) => {\n              return (\n                v.src ||\n                `${pullPath}?text=${Buffer.from(v.text, 'utf-8').toString('base64url')}`\n              );\n            }),\n          );\n          const realEntry = path.isAbsolute(option.entry)\n            ? normalizePath(path.relative(viteConfig.root, option.entry))\n            : option.entry;\n          const entryUrl = new URL(realEntry, localOrigin);\n          entryUrls.push(entryUrl.pathname + entryUrl.search);\n          res.end(\n            entryUrls.map((s) => `import ${JSON.stringify(s)};`).join('\\n'),\n          );\n        } else if (url.pathname === pullPath) {\n          setScriptHeader(res);\n          res.end(\n            Buffer.from(\n              url.searchParams.get('text') ?? '',\n              'base64url',\n            ).toString('utf-8'),\n          );\n        } else if (url.pathname === gmApiPath) {\n          setScriptHeader(res);\n          if (option.server.mountGmApi) {\n            res.end(\n              `;(${mountGmApiFn})(import.meta, ${JSON.stringify(gmIdentifiers)});`,\n            );\n          } else {\n            res.end('');\n          }\n        } else {\n          next();\n        }\n      });\n\n      if (option.server.open) {\n        const hash = simpleHash(viteConfig.configFile);\n        const cacheUserPath = `node_modules/.vite/__vite-plugin-monkey.cache.${hash}.user.js`;\n        let cacheComment = '';\n        if (await existFile(cacheUserPath)) {\n          cacheComment = (await fs.readFile(cacheUserPath)).toString('utf-8');\n        } else {\n          await fs.mkdir(path.dirname(cacheUserPath)).catch(() => {});\n        }\n        const newComment = await finalMonkeyOptionToComment(\n          option,\n          new Set(),\n          'serve',\n        );\n        const installUrl = Reflect.get(globalThis, restartStoreKey);\n        if (!isFirstBoot() && cacheComment != newComment && installUrl) {\n          openBrowser(installUrl);\n          setTimeout(() => {\n            console.log('[plugin-monkey] reopen, config comment has changed');\n          });\n        }\n        await fs.writeFile(cacheUserPath, newComment).catch(() => {});\n      }\n    },\n  };\n};\n", "import path, { join } from 'node:path';\nimport { exec } from 'node:child_process';\nimport type { ExecOptions } from 'node:child_process';\nimport open from 'open';\nimport type { Options } from 'open';\nimport spawn from 'cross-spawn';\nimport colors from 'picocolors';\nimport { compatResolve } from './others';\n\nconst VITE_PACKAGE_DIR = path.dirname(compatResolve('vite/package.json'));\n\n// copy from https://github.com/vitejs/vite/blob/main/packages/vite/src/node/server/openBrowser.ts\n\n/**\n * Reads the BROWSER environment variable and decides what to do with it.\n */\nexport function openBrowser(url: string, opt?: string): void {\n  // The browser executable to open.\n  // See https://github.com/sindresorhus/open#app for documentation.\n  const browser = typeof opt === 'string' ? opt : process.env.BROWSER || '';\n  if (browser.toLowerCase().endsWith('.js')) {\n    executeNodeScript(browser, url);\n  } else if (browser.toLowerCase() !== 'none') {\n    const browserArgs = process.env.BROWSER_ARGS\n      ? process.env.BROWSER_ARGS.split(' ')\n      : [];\n    startBrowserProcess(browser, browserArgs, url);\n  }\n}\n\nfunction executeNodeScript(scriptPath: string, url: string) {\n  const extraArgs = process.argv.slice(2);\n  const child = spawn(process.execPath, [scriptPath, ...extraArgs, url], {\n    stdio: 'inherit',\n  });\n  child.on('close', (code) => {\n    if (code !== 0) {\n      console.error(\n        '[plugin-monkey] ' +\n          colors.red(\n            `\\nThe script specified as BROWSER environment variable failed.\\n\\n${colors.cyan(\n              scriptPath,\n            )} exited with code ${code}.`,\n          ),\n        { error: null },\n      );\n    }\n  });\n}\n\nconst supportedChromiumBrowsers = [\n  'Google Chrome Canary',\n  'Google Chrome Dev',\n  'Google Chrome Beta',\n  'Google Chrome',\n  'Microsoft Edge',\n  'Brave Browser',\n  'Vivaldi',\n  'Chromium',\n];\n\nasync function startBrowserProcess(\n  browser: string | undefined,\n  browserArgs: string[],\n  url: string,\n) {\n  // If we're on OS X, the user hasn't specifically\n  // requested a different browser, we can try opening\n  // a Chromium browser with AppleScript. This lets us reuse an\n  // existing tab when possible instead of creating a new one.\n  const preferredOSXBrowser =\n    browser === 'google chrome' ? 'Google Chrome' : browser;\n  const shouldTryOpenChromeWithAppleScript =\n    process.platform === 'darwin' &&\n    (!preferredOSXBrowser ||\n      supportedChromiumBrowsers.includes(preferredOSXBrowser));\n\n  if (shouldTryOpenChromeWithAppleScript) {\n    try {\n      const ps = await execAsync('ps cax');\n      const openedBrowser =\n        preferredOSXBrowser && ps.includes(preferredOSXBrowser)\n          ? preferredOSXBrowser\n          : supportedChromiumBrowsers.find((b) => ps.includes(b));\n      if (openedBrowser) {\n        // Try our best to reuse existing tab with AppleScript\n        await execAsync(\n          `osascript openChrome.applescript \"${url}\" \"${openedBrowser}\"`,\n          {\n            cwd: join(VITE_PACKAGE_DIR, 'bin'),\n          },\n        );\n        return true;\n      }\n    } catch {\n      // Ignore errors\n    }\n  }\n\n  // Another special case: on OS X, check if BROWSER has been set to \"open\".\n  // In this case, instead of passing the string `open` to `open` function (which won't work),\n  // just ignore it (thus ensuring the intended behavior, i.e. opening the system browser):\n  // https://github.com/facebook/create-react-app/pull/1690#issuecomment-283518768\n  if (process.platform === 'darwin' && browser === 'open') {\n    browser = undefined;\n  }\n\n  // Fallback to open\n  // (It will always open new tab)\n  try {\n    const options: Options = browser\n      ? { app: { name: browser, arguments: browserArgs } }\n      : {};\n\n    new Promise((_, reject) => {\n      open(url, options)\n        .then((subprocess) => {\n          subprocess.on('error', reject);\n        })\n        .catch(reject);\n    }).catch((err) => {\n      console.error('[plugin-monkey] ' + (err.stack || err.message));\n    });\n\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nfunction execAsync(command: string, options?: ExecOptions): Promise<string> {\n  return new Promise((resolve, reject) => {\n    exec(command, options, (error, stdout) => {\n      if (error) {\n        reject(error);\n      } else {\n        resolve(stdout.toString());\n      }\n    });\n  });\n}\n", "import type { Plugin } from 'vite';\nimport { virtualHtmlTemplate, fcToHtml } from '../utils/template';\nimport { installUserPath } from './server';\n\nexport const virtualHtmlFactory = (): Plugin => {\n  return {\n    name: 'monkey:virtualHtml',\n    apply: 'serve',\n    configureServer(server) {\n      server.middlewares.use(async (req, res, next) => {\n        const url = req.url || '/';\n        if (['/', '/index.html'].includes(url)) {\n          res.setHeader('content-type', 'text/html');\n          res.setHeader('cache-control', 'no-cache');\n          res.setHeader('access-control-allow-origin', '*');\n          return res.end(fcToHtml(virtualHtmlTemplate, installUserPath));\n        }\n        next();\n      });\n    },\n  };\n};\n", "import { buildBundleFactory } from './buildBundle';\nimport { configFactory } from './config';\nimport { externalGlobalsFactory } from './externalGlobals';\nimport { externalLoaderFactory } from './externalLoader';\nimport { externalResourcePlugin } from './externalResource';\nimport { fixAssetUrlFactory } from './fixAssetUrl';\nimport { fixClientFactory } from './fixClient';\nimport { fixCssUrlFactory } from './fixCssUrl';\nimport { perviewFactory } from './perview';\nimport { redirectClientFactory } from './redirectClient';\nimport { serverFactory } from './server';\nimport { virtualHtmlFactory } from './virtualHtml';\n\nconst factorys = [\n  configFactory,\n\n  virtualHtmlFactory,\n  fixClientFactory,\n  fixAssetUrlFactory,\n  fixCssUrlFactory,\n  serverFactory,\n  perviewFactory,\n\n  redirectClientFactory,\n  externalGlobalsFactory,\n  externalLoaderFactory,\n  externalResourcePlugin,\n  buildBundleFactory,\n];\n\nexport default factorys;\n", "import type { ModuleToUrlFc } from './utils/types';\n\n/**\n * `https://cdn.jsdelivr.net/npm/${name}@${version}/${pathname}`\n * @param exportVarName cdn-exportVarName or resourceName\n * @param pathname jsdelivr support file combine, normally you don't need set pathname\n * @see https://www.jsdelivr.com/features\n */\nexport const jsdelivr = (\n  exportVarName = '',\n  pathname = '',\n): [string, ModuleToUrlFc] => {\n  return [\n    exportVarName,\n    (version, name, _importName = '', resolveName = '') => {\n      const p = pathname || resolveName;\n      if (p) {\n        return `https://cdn.jsdelivr.net/npm/${name}@${version}/${p}`;\n      } else {\n        return `https://cdn.jsdelivr.net/npm/${name}@${version}`;\n      }\n    },\n  ];\n};\n\n/**\n * `https://fastly.jsdelivr.net/npm/${name}@${version}/${pathname}`\n * @param exportVarName cdn-exportVarName or resourceName\n * @param pathname jsdelivr support file combine, normally you don't need set pathname\n * @see https://www.jsdelivr.com/features\n */\nexport const jsdelivrFastly = (\n  exportVarName = '',\n  pathname = '',\n): [string, ModuleToUrlFc] => {\n  return [\n    exportVarName,\n    (version, name, _importName = '', resolveName = '') => {\n      const p = pathname || resolveName;\n      if (p) {\n        return `https://fastly.jsdelivr.net/npm/${name}@${version}/${p}`;\n      } else {\n        return `https://fastly.jsdelivr.net/npm/${name}@${version}`;\n      }\n    },\n  ];\n};\n\n/**\n * `https://unpkg.com/${name}@${version}/${pathname}`\n * @param exportVarName cdn-exportVarName or resourceName\n * @see https://unpkg.com/\n */\nexport const unpkg = (\n  exportVarName = '',\n  pathname = '',\n): [string, ModuleToUrlFc] => {\n  return [\n    exportVarName,\n    (version, name, _importName = '', resolveName = '') => {\n      const p = pathname || resolveName;\n      if (p) {\n        return `https://unpkg.com/${name}@${version}/${p}`;\n      } else {\n        return `https://unpkg.com/${name}@${version}`;\n      }\n    },\n  ];\n};\n\n/**\n * `https://lf9-cdn-tos.bytecdntp.com/cdn/expire-10-y/${name}/${version}/${pathname}`\n * @param exportVarName cdn-exportVarName or resourceName\n * @see https://cdn.bytedance.com/\n */\nexport const bytecdntp = (\n  exportVarName = '',\n  pathname = '',\n): [string, ModuleToUrlFc] => {\n  return [\n    exportVarName,\n    (version, name, _importName = '', resolveName = '') => {\n      const p = pathname || resolveName;\n      return `https://lf9-cdn-tos.bytecdntp.com/cdn/expire-10-y/${name}/${version}/${p}`;\n    },\n  ];\n};\n\n/**\n * `https://fastly.jsdelivr.net/npm/${name}@${version}/${pathname}`\n * @deprecated bootcdn will return virus-infected code. Please stop using it and switch to other sources\n */\nexport const bootcdn = (\n  exportVarName = '',\n  pathname = '',\n): [string, ModuleToUrlFc] => {\n  console.warn(\n    '[plugin-monkey] bootcdn will return virus-infected code. Please stop using it and switch to other sources. now it will return jsdelivr url.',\n  );\n  return [\n    exportVarName,\n    (version, name, _importName = '', resolveName = '') => {\n      const p = pathname || resolveName;\n      return `https://fastly.jsdelivr.net/npm/${name}@${version}/${p}`;\n    },\n  ];\n};\n\n/**\n * `https://lib.baomitu.com/${name}/${version}/${pathname}`\n * @param exportVarName cdn-exportVarName or resourceName\n * @see https://cdn.baomitu.com/\n */\nexport const baomitu = (\n  exportVarName = '',\n  pathname = '',\n): [string, ModuleToUrlFc] => {\n  return [\n    exportVarName,\n    (version, name, _importName = '', resolveName = '') => {\n      const p = pathname || resolveName;\n      return `https://lib.baomitu.com/${name}/${version}/${p}`;\n    },\n  ];\n};\n\n/**\n * `https://fastly.jsdelivr.net/npm/${name}@${version}/${pathname}`\n * @deprecated staticfile will return virus-infected code. Please stop using it and switch to other sources\n */\nexport const staticfile = (\n  exportVarName = '',\n  pathname = '',\n): [string, ModuleToUrlFc] => {\n  console.warn(\n    '[plugin-monkey] staticfile will return virus-infected code. Please stop using it and switch to other sources. now it will return jsdelivr url.',\n  );\n  return [\n    exportVarName,\n    (version, name, _importName = '', resolveName = '') => {\n      const p = pathname || resolveName;\n      return `https://fastly.jsdelivr.net/npm/${name}@${version}/${p}`;\n    },\n  ];\n};\n/**\n * `https://cdnjs.cloudflare.com/ajax/libs/${name}/${version}/${pathname}`\n * @param exportVarName cdn-exportVarName or resourceName\n * @see https://cdnjs.com/libraries\n */\nexport const cdnjs = (\n  exportVarName = '',\n  pathname = '',\n): [string, ModuleToUrlFc] => {\n  return [\n    exportVarName,\n    (version, name, _importName = '', resolveName = '') => {\n      const p = pathname || resolveName;\n      return `https://cdnjs.cloudflare.com/ajax/libs/${name}/${version}/${p}`;\n    },\n  ];\n};\n\n/**\n * `https://unpkg.zhimg.com/${name}/${version}/${pathname}`\n * @param exportVarName cdn-exportVarName or resourceName\n * @link https://unpkg.zhimg.com/\n */\nexport const zhimg = (\n  exportVarName = '',\n  pathname = '',\n): [string, ModuleToUrlFc] => {\n  return [\n    exportVarName,\n    (version, name, _importName = '', resolveName = '') => {\n      const p = pathname || resolveName;\n      return `https://unpkg.zhimg.com/${name}@${version}/${p}`;\n    },\n  ];\n};\n\n/**\n * `https://npm.elemecdn.com/${name}@${version}/${pathname}`\n * @param exportVarName cdn-exportVarName or resourceName\n */\nexport const elemecdn = (\n  exportVarName = '',\n  pathname = '',\n): [string, ModuleToUrlFc] => {\n  return [\n    exportVarName,\n    (version, name, _importName = '', resolveName = '') => {\n      const p = pathname || resolveName;\n      return `https://npm.elemecdn.com/${name}@${version}/${p}`;\n    },\n  ];\n};\n\n/**\n * `https://code.bdstatic.com/npm/${name}@${version}/${pathname}`\n * @param exportVarName cdn-exportVarName or resourceName\n */\nexport const bdstatic = (\n  exportVarName = '',\n  pathname = '',\n): [string, ModuleToUrlFc] => {\n  return [\n    exportVarName,\n    (version, name, _importName = '', resolveName = '') => {\n      const p = pathname || resolveName;\n      return `https://code.bdstatic.com/npm/${name}@${version}/${p}`;\n    },\n  ];\n};\n\n/**\n * `https://registry.npmmirror.com/${name}/${version}/files/${pathname}`\n * @param exportVarName cdn-exportVarName or resourceName\n */\nexport const npmmirror = (\n  exportVarName = '',\n  pathname = '',\n): [string, ModuleToUrlFc] => {\n  return [\n    exportVarName,\n    (version, name, _importName = '', resolveName = '') => {\n      const p = pathname || resolveName;\n      if (p) {\n        return `https://registry.npmmirror.com/${name}/${version}/files/${p}`;\n      } else {\n        return `https://registry.npmmirror.com/${name}/${version}/files`;\n      }\n    },\n  ];\n};\n", "import { miniCode, stringifyFunction } from './others';\nimport { jsdelivr } from '../cdn';\nimport type {\n  ResolvedMonkeyOption,\n  IArray,\n  Thenable,\n  Mod2UrlFn,\n  MonkeyOption,\n  Pkg2UrlFn,\n  PkgOptions,\n} from './types';\nimport { getProjectPkg } from './pkg';\n\nexport const resolvedOption = async (\n  pluginOption: MonkeyOption,\n): Promise<ResolvedMonkeyOption> => {\n  if (pluginOption.format) {\n    setTimeout(() => {\n      console.log(\n        '[vite-plugin] `format` option is deprecated, use `align` and `generate` instead',\n      );\n    });\n  }\n\n  const build = pluginOption.build ?? {};\n\n  const { externalResource = {} } = build;\n  const externalResource2: Record<\n    string,\n    {\n      resourceUrl: Pkg2UrlFn;\n      resourceName: Pkg2UrlFn;\n      loader?: (pkgOptions: PkgOptions) => unknown;\n      nodeLoader?: (pkgOptions: PkgOptions) => Thenable<string>;\n    }\n  > = {};\n\n  for (const [k, v] of Object.entries(externalResource)) {\n    if (typeof v == 'string') {\n      externalResource2[k] = {\n        resourceName: () => k,\n        resourceUrl: () => v,\n      };\n    } else if (typeof v == 'function') {\n      externalResource2[k] = {\n        resourceName: () => k,\n        resourceUrl: v,\n      };\n    } else if (v instanceof Array) {\n      let resourceUrl2: Pkg2UrlFn;\n      let resourceName2: Pkg2UrlFn = () => k;\n      const [resourceName, resourceUrl] = v;\n      if (typeof resourceName == 'string') {\n        resourceName2 = (pkg) => resourceName || pkg.importName;\n      } else {\n        resourceName2 = (pkg) =>\n          resourceName(pkg.version, pkg.name, pkg.importName, pkg.resolveName);\n      }\n      if (typeof resourceUrl == 'string') {\n        resourceUrl2 = () => resourceUrl;\n      } else {\n        resourceUrl2 = (pkg) =>\n          resourceUrl(pkg.version, pkg.name, pkg.importName, pkg.resolveName);\n      }\n      externalResource2[k] = {\n        resourceName: resourceName2,\n        resourceUrl: resourceUrl2,\n      };\n    } else {\n      const { resourceUrl, loader, nodeLoader, resourceName } = v;\n      let resourceUrl2: Pkg2UrlFn;\n      let resourceName2: Pkg2UrlFn = () => k;\n      let nodeLoader2:\n        | undefined\n        | ((pkgOptions: PkgOptions) => Thenable<string>) = undefined;\n      if (typeof resourceUrl == 'string') {\n        resourceUrl2 = () => resourceUrl;\n      } else {\n        resourceUrl2 = resourceUrl;\n      }\n      if (typeof resourceName == 'string') {\n        resourceName2 = () => resourceName;\n      } else if (typeof resourceName == 'function') {\n        resourceName2 = resourceName;\n      }\n      if (typeof nodeLoader == 'function') {\n        nodeLoader2 = nodeLoader;\n      } else if (typeof nodeLoader == 'string') {\n        nodeLoader2 = () => nodeLoader;\n      }\n      externalResource2[k] = {\n        resourceName: resourceName2,\n        resourceUrl: resourceUrl2,\n        loader,\n        nodeLoader: nodeLoader2,\n      };\n    }\n  }\n\n  const server = pluginOption.server ?? {};\n\n  const { prefix } = server;\n  let prefix2: (name: string) => string = (s) => 'server:' + s;\n  if (typeof prefix == 'function') {\n    prefix2 = prefix;\n  } else if (typeof prefix == 'string') {\n    prefix2 = () => prefix;\n  } else if (prefix === false) {\n    prefix2 = (name: string) => name;\n  }\n  const externalGlobals2 = build?.externalGlobals ?? {};\n  const externalGlobals: [string, IArray<string | Mod2UrlFn>][] = [];\n  if (externalGlobals2 instanceof Array) {\n    externalGlobals2.forEach((s) => externalGlobals.push(s));\n  } else {\n    Object.entries(externalGlobals2).forEach((s) => externalGlobals.push(s));\n  }\n\n  const projectPkg = await getProjectPkg();\n\n  const { grant = [], $extra = [] } = pluginOption.userscript ?? {};\n  let {\n    name = {},\n    description = {},\n    'exclude-match': excludeMatch = [],\n    match = [],\n    exclude = [],\n    include = [],\n    antifeature = [],\n    require = [],\n    connect = [],\n    webRequest = [],\n  } = pluginOption.userscript ?? {};\n  if (typeof name == 'string') {\n    name = { '': name };\n  } else if (!('' in name)) {\n    name = { ...(projectPkg.name ? { '': projectPkg.name } : {}), ...name };\n  }\n  if (typeof description == 'string') {\n    description = {\n      '': description,\n    };\n  } else if (!('' in description) && projectPkg.description) {\n    description = { '': projectPkg.description, ...description };\n  }\n  if (!(excludeMatch instanceof Array)) {\n    excludeMatch = [excludeMatch];\n  }\n  if (!(match instanceof Array)) {\n    match = [match];\n  }\n  if (!(exclude instanceof Array)) {\n    exclude = [exclude];\n  }\n  if (!(include instanceof Array)) {\n    include = [include];\n  }\n  if (!(antifeature instanceof Array)) {\n    antifeature = [antifeature];\n  }\n  if (!(require instanceof Array)) {\n    require = [require];\n  }\n  if (!(connect instanceof Array)) {\n    connect = [connect];\n  }\n  if (!(webRequest instanceof Array)) {\n    webRequest = [webRequest];\n  }\n\n  const grantSet = new Set<string>();\n  if (typeof grant == 'string') {\n    grantSet.add(grant);\n  } else if (grant instanceof Array) {\n    grant.forEach((s) => grantSet.add(s));\n  }\n\n  const extra: [string, ...string[]][] = [];\n  ($extra instanceof Array ? $extra : Object.entries($extra)).forEach(\n    ([k, v]) => {\n      extra.push([k, ...(v instanceof Array ? v : [v])]);\n    },\n  );\n\n  const {\n    icon64,\n    icon64URL,\n    icon,\n    iconURL,\n    namespace,\n    version = projectPkg.version,\n    author = projectPkg.author,\n    copyright,\n    downloadURL,\n    defaulticon,\n    contributionURL,\n    updateURL,\n    supportURL = projectPkg.bugs,\n    homepageURL = projectPkg.homepage,\n    homepage = projectPkg.homepage,\n    website,\n    license = projectPkg.license,\n    incompatible,\n    source = projectPkg.repository,\n    resource = {},\n    noframes = false,\n    'run-at': runAt,\n    'inject-into': injectInto,\n    contributionAmount,\n    compatible,\n    sandbox,\n    tag,\n    unwrap = false,\n  } = pluginOption.userscript ?? {};\n\n  const { fileName = projectPkg.name + '.user.js' } = build;\n  let { metaFileName } = build;\n  if (typeof metaFileName == 'string') {\n    const t = metaFileName;\n    metaFileName = () => t;\n  } else if (metaFileName === true) {\n    metaFileName = () => fileName.replace(/\\.user\\.js$/, '.meta.js');\n  } else if (metaFileName === false) {\n    metaFileName = undefined;\n  }\n\n  const metaFileFc = metaFileName;\n\n  const cssSideEffects =\n    build.cssSideEffects ||\n    (() => {\n      return (e: string) => {\n        // @ts-ignore\n        if (typeof GM_addStyle == 'function') {\n          // @ts-ignore\n          GM_addStyle(e);\n          return;\n        }\n        const o = document.createElement('style');\n        o.textContent = e;\n        document.head.append(o);\n      };\n    });\n\n  const config: ResolvedMonkeyOption = {\n    userscript: {\n      name,\n      namespace,\n      version,\n      icon64,\n      icon64URL,\n      icon,\n      iconURL,\n      author,\n      copyright,\n      downloadURL,\n      defaulticon,\n      contributionURL,\n      updateURL,\n      supportURL,\n      homepageURL,\n      homepage,\n      website,\n      license,\n      incompatible,\n      source,\n      resource,\n      noframes,\n      'run-at': runAt,\n      'inject-into': injectInto,\n      contributionAmount,\n      compatible,\n      'exclude-match': excludeMatch.map((s) => String(s)),\n      match: match.map((s) => String(s)),\n      include: include.map((s) => String(s)),\n      exclude: exclude.map((s) => String(s)),\n      antifeature,\n      require,\n      connect,\n      description,\n      $extra: extra,\n      grant: grantSet,\n      sandbox,\n      tag: tag ? (tag instanceof Array ? tag : [tag]) : [],\n      unwrap,\n      webRequest: webRequest.map((w) => JSON.stringify(w)),\n    },\n    clientAlias: pluginOption.clientAlias ?? '$',\n    entry: pluginOption.entry,\n    align: pluginOption.align === false ? 0 : (pluginOption.align ?? 2),\n    generate: pluginOption.generate ?? ((uOptions) => uOptions.userscript),\n    server: {\n      mountGmApi: server.mountGmApi ?? false,\n      open:\n        server.open ??\n        (process.platform == 'win32' || process.platform == 'darwin'),\n      prefix: prefix2,\n    },\n    build: {\n      fileName,\n      metaFileName: metaFileFc ? () => metaFileFc(fileName) : undefined,\n      autoGrant: build.autoGrant ?? true,\n      externalGlobals: externalGlobals,\n      externalResource: externalResource2,\n    },\n    collectRequireUrls: [],\n    collectResource: {},\n    globalsPkg2VarName: {},\n    requirePkgList: [],\n    systemjs: build.systemjs ?? jsdelivr()[1],\n    cssSideEffects: async (css) => {\n      const codeOrFc = await cssSideEffects(css);\n      if (typeof codeOrFc == 'string') {\n        return codeOrFc;\n      }\n      return miniCode(stringifyFunction(codeOrFc, css), 'js');\n    },\n  };\n\n  return config;\n};\n", "import type { Plugin } from 'vite';\nimport factorys from './plugins';\nimport { resolvedOption } from './utils/option';\nimport type { MonkeyOption, ResolvedMonkeyOption } from './utils/types';\nimport { dataUrl } from './utils/others';\nimport type { InlinePreset } from 'unimport';\nimport { gmIdentifiers } from './utils/gmApi';\n\nexport type * from './types';\nexport * as cdn from './cdn';\n\nexport default (pluginOption: MonkeyOption): Plugin[] => {\n  let option: ResolvedMonkeyOption;\n  return factorys.map((f) =>\n    f(async () => {\n      return option || resolvedOption(pluginOption).then((v) => (option = v));\n    }),\n  );\n};\n\n/**\n * GM api preset when you use unimport or unplugin-auto-import\n *\n * Note, there is not comment in automatically generated unimport.d.ts/auto-imports.d.ts file\n */\nconst unimportPreset = {\n  from: 'vite-plugin-monkey/dist/client',\n  imports: ['GM', ...gmIdentifiers, 'unsafeWindow', 'monkeyWindow'],\n} satisfies InlinePreset;\n\nexport const util = {\n  dataUrl,\n  unimportPreset,\n};\n"], "mappings": ";;;;;;;AAEA,SAAS,aAAa;;;ACFf,IAAM,gBAAgB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,YAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAOO,IAAM,aAAa,CAAC,GAAG,WAAW,GAAG,eAAe,GAAG,gBAAgB;;;ACoGvE,IAAM,6BAA6B,OACxC,QACA,iBACA,SACoB;AACpB,QAAM,EAAE,YAAY,oBAAoB,gBAAgB,IAAI;AAC5D,MAAI,WAAoC,CAAC;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAAA;AAAA,IACA,iBAAiB;AAAA,IAEjB,eAAe;AAAA,IACf,UAAU;AAAA,IAEV;AAAA,IACA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO,QAAQ;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACrB,QAAI,OAAO,KAAK,UAAU;AACxB,eAAS,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IACtB;AAAA,EACF,CAAC;AACD,SAAO,QAAQ,IAAI,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACvC,QAAI,KAAK,IAAI;AACX,eAAS,KAAK,CAAC,QAAQ,CAAC,CAAC;AAAA,IAC3B,OAAO;AACL,eAAS,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;AAAA,IAChC;AAAA,EACF,CAAC;AACD,SAAO,QAAQ,WAAW,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AAC9C,QAAI,KAAK,IAAI;AACX,eAAS,KAAK,CAAC,eAAe,CAAC,CAAC;AAAA,IAClC,OAAO;AACL,eAAS,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAAA,IACvC;AAAA,EACF,CAAC;AAED,SAAO,QAAQ;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACrB,MAAE,QAAQ,CAAC,OAAO;AAChB,eAAS,KAAK,CAAC,GAAG,EAAE,CAAC;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AAED,GAAC,GAAGA,UAAS,GAAG,kBAAkB,EAAE,QAAQ,CAAC,MAAM;AACjD,aAAS,KAAK,CAAC,WAAW,CAAC,CAAC;AAAA,EAC9B,CAAC;AAED,SAAO,QAAQ,EAAE,GAAG,UAAU,GAAG,gBAAgB,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACtE,aAAS,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;AAAA,EAClC,CAAC;AAED,UAAQ,QAAQ,CAAC,MAAM;AACrB,aAAS,KAAK,CAAC,WAAW,CAAC,CAAC;AAAA,EAC9B,CAAC;AACD,MAAI,QAAQ,CAAC,MAAM;AACjB,aAAS,KAAK,CAAC,OAAO,CAAC,CAAC;AAAA,EAC1B,CAAC;AAED,aAAW,QAAQ,CAAC,MAAM;AACxB,aAAS,KAAK,CAAC,cAAc,CAAC,CAAC;AAAA,EACjC,CAAC;AAED,MAAI,MAAM,IAAI,MAAM,GAAG;AACrB,aAAS,KAAK,CAAC,SAAS,MAAM,CAAC;AAAA,EACjC,WAAW,MAAM,IAAI,GAAG,GAAG;AACzB,eAAW,QAAQ,CAAC,MAAM;AACxB,eAAS,KAAK,CAAC,SAAS,CAAC,CAAC;AAAA,IAC5B,CAAC;AAAA,EACH,OAAO;AACL,yBAAI,IAAI,CAAC,GAAG,MAAM,KAAK,gBAAgB,OAAO,CAAC,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC,GAAE;AAAA,MAClE,CAAC,MAAM;AACL,YAAI,CAAC,EAAE,KAAK,EAAG;AACf,iBAAS,KAAK,CAAC,SAAS,CAAC,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACA,cAAY,QAAQ,CAAC,EAAE,aAAAC,cAAa,MAAM,KAAAC,KAAI,MAAM;AAClD,aAAS,KAAK;AAAA,MACZA,OAAM,eAAeA,IAAG,KAAK;AAAA,MAC7B;AAAA,MACAD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,MAAI,UAAU;AACZ,aAAS,KAAK,CAAC,UAAU,CAAC;AAAA,EAC5B;AACA,MAAI,QAAQ;AACV,aAAS,KAAK,CAAC,QAAQ,CAAC;AAAA,EAC1B;AACA,WAAS,KAAK,GAAG,MAAM;AAEvB,aAAW,kBAAkB,QAAQ;AAGrC,MAAI,OAAO,SAAS,GAAG;AACrB,UAAM,YAAY,CAAC,gBAAyC;AAC1D,UAAI,YAAY,UAAU,EAAG;AAC7B,YAAME,UAAS,KAAK,IAAI,GAAG,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC;AAC9D,kBAAY,QAAQ,CAAC,MAAM;AACzB,UAAE,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,OAAO,QAAQA,OAAM;AAAA,MAC1C,CAAC;AAAA,IACH;AAEA,cAAU,SAAS,OAAO,CAAC,MAAM,EAAE,CAAC,KAAK,UAAU,CAAC;AACpD;AAAA,MACE,SAAS;AAAA,QACP,CAAC,MAAM,EAAE,CAAC,KAAK,iBAAiB,EAAE,CAAC,EAAE,WAAW,cAAc;AAAA,MAChE;AAAA,IACF;AAEA,UAAM,SAAS,KAAK,IAAI,GAAG,SAAS,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC;AAC3D,aAAS,QAAQ,CAAC,MAAM;AACtB,QAAE,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,OAAO,QAAQ,MAAM;AAAA,IAC1C,CAAC;AAAA,EACH;AAEA,QAAM,UAAU;AAAA,IACd;AAAA,IACA,GAAG,SAAS;AAAA,MACV,CAAC,SACC,MACA,KACG,IAAI,CAAC,MAAM;AACV,eAAO,EAAE,SAAS,GAAM,IAAI,IAAI,IAAI;AAAA,MACtC,CAAC,EACA,KAAK,EAAE,EACP,QAAQ;AAAA,IACf;AAAA,IACA;AAAA,EACF,EACG,IAAI,CAAC,MAAM,QAAW,CAAC,EACvB,KAAK,IAAI;AAEZ,SAAO,OAAO,SAAS,EAAE,YAAY,SAAS,KAAK,CAAC;AACtD;AAEA,IAAM,aAAa,CAAC,GAA0B,MAA6B;AACzE,QAAM,SAAS,KAAK,IAAI,EAAE,QAAQ,EAAE,MAAM;AAC1C,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,QAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG;AACf,aAAO;AAAA,IACT,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG;AACtB,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,EAAE,SAAS,EAAE,QAAQ;AACvB,WAAO;AAAA,EACT,WAAW,EAAE,SAAS,EAAE,QAAQ;AAC9B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAM,oBAAoB,CAAC,OAAgC;AACzD,QAAM,SAAS,CACb,cAC4B;AAC5B,UAAM,eAAwC,CAAC;AAC/C,UAAM,YAAqC,CAAC;AAC5C,OAAG,QAAQ,CAAC,OAAO,UAAU;AAC3B,UAAI,CAAC,UAAU,OAAO,KAAK,GAAG;AAC5B,qBAAa,KAAK,KAAK;AAAA,MACzB,OAAO;AACL,kBAAU,KAAK,KAAK;AAAA,MACtB;AAAA,IACF,CAAC;AACD,SAAK;AACL,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM;AAAA,IAC3B,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,WAAW,OAAO,CAAC;AAAA,IACrC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW;AAAA,IAChC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS;AAAA,IAC9B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ;AAAA,IAC7B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,aAAa;AAAA,IAClC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,WAAW,cAAc,CAAC;AAAA,IAC5C,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS;AAAA,IAC9B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW;AAAA,IAEhC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM;AAAA,IAC3B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS;AAAA,IAC9B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ;AAAA,IAC7B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW;AAAA,IAChC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,aAAa;AAAA,IAElC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU;AAAA,IAC/B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,aAAa;AAAA,IAClC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS;AAAA,IAC9B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ;AAAA,IAE7B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,YAAY;AAAA,IACjC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,aAAa;AAAA,IAClC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW;AAAA,IAEhC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS;AAAA,IAC9B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,OAAO;AAAA,IAC5B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS;AAAA,IAC9B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,eAAe;AAAA,IACpC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,YAAY;AAAA,IAEjC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS;AAAA,IAE9B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU,EAAE,KAAK,UAAU;AAAA,IAEhD,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS;AAAA,IAC9B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,KAAK;AAAA,IAE1B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS;AAAA,IAE9B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,OAAO,EAAE,KAAK,UAAU;AAAA,IAE7C,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,aAAa;AAAA,IAClC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ;AAAA,IAE7B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,YAAY;AAAA,IACjC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,cAAc;AAAA,IACnC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,aAAa,EAAE,KAAK,UAAU;AAAA,IACnD,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,WAAW,cAAc,CAAC,EAAE,KAAK,UAAU;AAAA,IAC7D,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,oBAAoB;AAAA,IACzC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,iBAAiB;AAAA,IACtC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU;AAAA,IAC/B,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ;AAAA,IAC7B;AAAA,EACF,EAAE,KAAK,CAAC;AACV;;;ACpdA,YAAY,eAAe;AAIpB,IAAM,eAAe,CAC1B,SACA,QACA,eACA,WACgB;AAChB,QAAM,QAAQ,oBAAI,IAAY;AAC9B,MAAI,eAAe;AACjB,UAAM,IAAI,aAAa;AAAA,EACzB;AACA,aAAW,SAAS,QAAQ;AAC1B,QAAI,QAAQ;AAEV,YAAM,UAAU,OAAO,OAAO,MAAM,OAAO;AAC3C,cAAQ,QAAQ,CAAC,MAAM;AACrB,cAAM,OAAO,EAAE;AACf,YAAI,MAAM;AACR,gBAAM,IAAI,IAAI;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,IAAI,MAAM,IAAI;AAAA,EACtB;AACA,QAAM,gBAAgB,IAAI;AAAA,IACxB,WAAW,OAAO,CAAC,MAAM,EAAE,SAAS,GAAG,CAAC;AAAA,EAC1C;AACA,QAAM,cAAc,CAAC,GAAW,MAAuB;AACrD,QAAI,EAAE,SAAS,CAAC,GAAG;AACjB,aAAO,MAAM,kBAAkB,KAAK,MAAM,mBAAmB;AAAA,IAC/D;AACA,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,OAAO;AAAA,IAC7B,WACG,OAAO,CAAC,MAAM,EAAE,WAAW,SAAS,CAAC,EACrC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAc,YAAY,GAAG,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAAA,EAC5E;AACA,QAAM,oBAAoB,IAAI;AAAA,IAC5B,WAAW,OAAO,CAAC,MAAM,CAAC,EAAE,SAAS,GAAG,CAAC;AAAA,EAC3C;AACA,QAAM,SAAS,oBAAI,IAAY;AAC/B,QAAM,kBAAkB,CAAC,SAA0B;AACjD,QAAI,kBAAkB,IAAI,IAAI,GAAG;AAC/B,aAAO,IAAI,IAAI;AACf,wBAAkB,OAAO,IAAI;AAC7B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,cAAc,CAAC,SAA0B;AAC7C,eAAW,cAAc,cAAc,OAAO,GAAG;AAC/C,UAAI,KAAK,SAAS,UAAU,KAAK,gBAAgB,UAAU,IAAI,IAAI,GAAG;AACpE,eAAO,IAAI,UAAU;AACrB,sBAAc,OAAO,UAAU;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,CAAC,KAAK,KAAK,EAAG;AAClB,UAAM,MAAM,QAAQ,MAAM,IAAI;AAC9B,IAAU;AAAA,MACR;AAAA,MACA;AAAA,QACE,iBAAiB,MAAM;AACrB,cAAI,cAAc,SAAS,EAAG;AAC9B,cACE,KAAK,YACL,KAAK,OAAO,SAAS,gBACrB,KAAK,SAAS,SAAS,cACvB;AACA;AAAA,UACF;AACA,cACE,KAAK,OAAO,SAAS,kBACrB,KAAK,OAAO,SAAS,iBACrB;AACA,gBAAI,gBAAgB,KAAK,SAAS,IAAI,GAAG;AACvC;AAAA,YACF;AAAA,UACF;AACA,gBAAM,OAAO,KAAK,OAAO,OAAO,MAAM,KAAK,SAAS;AACpD,sBAAY,IAAI;AAAA,QAClB;AAAA,QACA,WAAW,MAAM;AAEf,0BAAgB,KAAK,IAAI;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,EAAE,GAAa,eAAK;AAAA,IACtB;AACA,QAAI,cAAc,QAAQ,KAAK,kBAAkB,QAAQ,GAAG;AAC1D;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACrGA,SAAS,eAAe;AACxB,OAAO,QAAQ;AACf,OAAO,UAAU;AACjB,SAAS,qBAAqB;AAE9B,SAAS,4BAA4B;AA+GrC,SAAS,UAAU,aAAa,qBAAqB;AAiCrD,OAAO,YAAY;AA7IZ,IAAM,cAAc,MAAe;AACxC,UAAQ,QAAQ,IAAI,YAAY,mBAAmB,KAAK,KAAK;AAC/D;AAEO,IAAM,gBAAgB,CAAC,OAAe;AAC3C,SAAO,QAAQ,IAAI,cAAc,QAAQ,IAAI,IAAI,SAAS,EAAE,IAAI;AAClE;AAEO,IAAM,YAAY,OAAOC,UAAiB;AAC/C,MAAI;AACF,YAAQ,MAAM,GAAG,KAAKA,KAAI,GAAG,OAAO;AAAA,EACtC,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEO,IAAM,WAAW,OAAO,MAAc,OAAqB,SAAS;AACzE,UACE,MAAM,qBAAqB,MAAM,cAAc,MAAM;AAAA,IACnD,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,eAAe;AAAA,EACjB,CAAC,GACD,KAAK,QAAQ;AACjB;AAEO,IAAM,gCAAgC,CAAC,eAAuB;AACnE,MAAI,IAAI;AACR,MAAI,aAAa;AACjB,SAAO,WAAW,SAAS,UAAU,GAAG;AACtC,iBAAa,KAAK,KAAK,IAAI,SAAS,EAAE,CAAC;AACvC;AAAA,EACF;AAEA,SAAO,eAAe,UAAU,IAAI,UAAU,kBAAkB,UAAU,OAAO,UAAU,YAAY,UAAU,YAAY,UAAU;AACzI;AAEA,gBAAuB,KAAK,SAAiB;AAC3C,QAAM,aAAa,MAAM,GAAG,QAAQ,OAAO,GAAG;AAAA,IAAI,CAAC,MACjD,KAAK,KAAK,SAAS,CAAC;AAAA,EACtB;AACA,SAAO,UAAU,SAAS,GAAG;AAC3B,UAAM,WAAW,UAAU,IAAI;AAC/B,UAAM,QAAQ,MAAM,GAAG,MAAM,QAAQ;AACrC,QAAI,MAAM,OAAO,GAAG;AAClB,YAAM;AAAA,IACR,WAAW,MAAM,YAAY,GAAG;AAC9B,gBAAU;AAAA,QACR,IAAI,MAAM,GAAG,QAAQ,QAAQ,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,UAAU,CAAC,CAAC;AAAA,MACnE;AAAA,IACF;AAAA,EACF;AACF;AAEO,IAAM,mBAAmB,OAC9B,QACA,WACG;AACH,QAAM,WAAqB,CAAC;AAC5B,SAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACzC,QAAI,EAAE,QAAQ,WAAW,EAAE,SAAS,MAAM,GAAG;AAC3C,eAAS,KAAK,EAAE,OAAO,SAAS,CAAC;AACjC,aAAO,OAAO,CAAC;AAAA,IACjB;AAAA,EACF,CAAC;AACD,QAAM,MAAM,SAAS,KAAK,EAAE,EAAE,KAAK;AACnC,MAAI,KAAK;AAEP,WAAO,MAAM,OAAO,eAAe,MAAS,MAAM,GAAM;AAAA,EAC1D;AACF;AAEO,IAAM,oBAAoB,CAC/B,OACG,SACA;AACH,SAAO,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC;AACjE;AAEO,IAAM,YAAY,CAAC,SAAyB;AACjD,SAAO,iCAAiC,mBAAmB,IAAI;AACjE;AAoBO,SAAS,QAAQ,OAAY,MAAuC;AACzE,MAAI,OAAO,MAAM,UAAU;AACzB,WAAO,UAAU,EAAE;AAAA,EACrB;AACA,SAAO,SAAS,kBAAkB,IAAI,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS;AAChE;AASO,IAAM,yBAAyB,CAAC,SAAiC;AACtE,QAAM,MAAM,cAAc,IAAI;AAG9B,QAAM,UAAU,SAAS;AAAA,IACvB,YAAY;AAAA,IACZ;AAAA,EACF;AACA,SAAO,QAAQ,IAAkB,CAAC,MAAM;AACtC,UAAM,MAAM,EAAE,QAAQ,OAAO;AAC7B,UAAM,WAAW,EAAE;AACnB,QAAI,OAAO;AACX,QAAI,UAAU,QAAQ,YAAY,MAAM;AACtC,aAAO,SAAS,QAAQ;AAAA,IAC1B;AACA,QAAI,KAAK;AACP,aAAO,EAAE,KAAK,KAAK;AAAA,IACrB,OAAO;AACL,aAAO;AAAA,QACL,KAAK;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAIO,IAAM,aAAa,CAAC,MAAM,OAAe;AAC9C,SAAO,OACJ,WAAW,KAAK,EAChB,OAAO,OAAO,EAAE,EAChB,OAAO,WAAW,EAClB,UAAU,GAAG,CAAC;AACnB;AAEO,IAAM,UAAU,CACrB,KACAC,UACoB;AACpB,MAAI,CAAC,IAAK,QAAO;AACjB,MAAI;AACF,WAAO,IAAI,IAAI,KAAKA,KAAI;AAAA,EAC1B,QAAQ;AAAA,EAAC;AACX;;;ACvKA,OAAOC,SAAQ;AACf,OAAO,YAAY;AAKnB,IAAM,WAAW,OAAO,cAAc,YAAY,GAAG;AAErD,IAAM,cAAmC,SAAS,uBAAuB;AAEzE,IAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AACF;AAGA,IAAM,2BAA2B;AAEjC,IAAM,wBAAwB,iBAAiB,IAAI,CAAC,MAAM;AACxD,SAAO,SAAS,QAAQ,cAAc,CAAC;AACzC,CAAC;AAEM,IAAM,mBAAmB,YAA+B;AAC7D,SAAO,QAAQ;AAAA,IACb,sBACG;AAAA,MAAI,CAAC,MACJC,IAAG,SAAS,GAAG,OAAO,EAAE;AAAA,QAAK,CAACC,OAC5BA,GACG,KAAK,EACL,QAAQ,qBAAqB,EAAE,EAC/B,QAAQ,cAAc,EAAE,EACxB,KAAK;AAAA,MACV;AAAA,IACF,EACC,OAAO,CAAC,QAAQ,QAAQ,wBAAwB,CAAC,CAAC;AAAA,EACvD;AACF;AAEO,IAAM,yBAAyB,CAAC,OAAsB;AAC3D,SAAO,iBACJ,IAAI,CAAC,MAAM;AACV,WAAO,GAAG,YAAY,SAAS,YAAY,MAAM,GAAG,CAAC;AAAA,EACvD,CAAC,EACA,OAAO,CAAC,QAAQ,wBAAwB,CAAC,CAAC;AAC/C;;;AC3CA,YAAYC,gBAAe;AAC3B,OAAO,iBAAiB;AAYxB,IAAM,cAAc,QAAQ;AAC5B,IAAM,oBAAoB;AAEnB,IAAM,uBAAuB,CAAC,cAA4B;AAC/D,QAAM,QAAkB,CAAC;AACzB,aAAW,SAAS,OAAO,OAAO,SAAS,GAAG;AAC5C,QAAI,MAAM,QAAQ,SAAS;AACzB,YAAM,KAAK,MAAM,IAAI;AAAA,IACvB;AAAA,EACF;AACA,MAAI,IAAI;AACR,MAAI,aAAa;AACjB,SAAO,MAAM,KAAK,CAAC,SAAS,KAAK,SAAS,UAAU,CAAC,GAAG;AACtD;AACA,iBAAa,oBAAoB,EAAE,SAAS,EAAE;AAAA,EAChD;AACA,SAAO;AACT;AAEA,IAAM,YAAY,CAChB,MACA,cACA,WAAW,GACX,iBACG;AACH,WAAS,IAAI,UAAU,IAAI,KAAK,QAAQ,KAAK;AAC3C,QAAI,aAAa,SAAS,KAAK,CAAC,CAAC,GAAG;AAClC;AAAA,IACF;AACA,WAAO,KAAK,WAAW,cAAc,CAAC;AAAA,EACxC;AACA,SAAO;AACT;AAEA,IAAM,WAAW,CACf,KACA,OACA,KACA,WACY;AACZ,QAAM,IAAI,IAAI,QAAQ,QAAQ,KAAK;AACnC,SAAO,KAAK,KAAK,IAAI,OAAO,SAAS;AACvC;AAEO,IAAM,2BAA2B,CACtC,SACA,OACA,eACG;AACH,MAAI,MAAM,QAAQ,SAAS;AACzB,UAAM,OAAO,MAAM;AACnB,QAAI,CAAC,KAAK,SAAS,OAAO,GAAG;AAC3B;AAAA,IACF;AACA,UAAM,MAAM,QAAQ,MAAM,IAAI;AAC9B,UAAM,WAAoC,CAAC;AAC3C,UAAM,gBAAwC,CAAC;AAC/C,IAAU;AAAA,MACR;AAAA,MACA;AAAA,QACE,gBAAgB,MAAM;AAEpB,mBAAS,KAAK,IAAI;AAAA,QACpB;AAAA,QACA,eAAe,MAAM;AACnB,cAAI,KAAK,UAAU,MAAM;AACvB,0BAAc,KAAK,IAAI;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,GAAa,iBAAM,UAAU,MAAM;AAAA,MAAC,EAAE;AAAA,IAC1C;AACA,QAAI,SAAS,SAAS,KAAK,cAAc,SAAS,GAAG;AACnD,YAAM,KAAK,IAAI,YAAY,IAAI;AAC/B,eAAS,QAAQ,CAAC,SAAS;AACzB,YACE,CAAC,UAAU,MAAM,MAAM,KAAK,KAAK,QAAQ,aAAa,QAAY,GAClE;AAEA,aAAG,WAAW,KAAK,QAAQ,aAAa,GAAG;AAC3C,aAAG,YAAY,KAAK,KAAK,GAAG;AAAA,QAC9B;AAGA,WAAG,OAAO,KAAK,OAAO,KAAK,QAAQ,aAAa,UAAU;AAAA,MAC5D,CAAC;AACD,oBAAc,QAAQ,CAAC,SAAS;AAE9B,WAAG,WAAW,KAAK,OAAO,GAAG,aAAa,KAAK,cAAc;AAC7D,WAAG,YAAY,KAAK,KAAK,QAAQ;AAAA,MACnC,CAAC;AACD,aAAO;AAAA,QACL,MAAM,GAAG,SAAS;AAAA,QAClB,KAAK,GAAG,YAAY;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACF;AAEO,IAAM,2BAA2B,CACtC,SACA,OACA,eACG;AACH,MAAI,MAAM,QAAQ,SAAS;AACzB,QAAI,CAAC,MAAM,KAAK,SAAS,UAAU,GAAG;AACpC;AAAA,IACF;AAEA,UAAM,gBAAgB,aAAa;AAEnC,UAAM,MAAM,QAAQ,MAAM,MAAM,IAAI;AACpC,UAAM,eAAsC,CAAC;AAC7C,UAAM,kBAAyC,CAAC;AAChD,UAAM,aAA+B,CAAC;AACtC,IAAU;AAAA,MACR;AAAA,MACA;AAAA,QACE,eAAe,MAAM;AACnB,cAAI,UAAU,KAAK,QAAQ;AACzB,kBAAM,EAAE,MAAM,KAAK,IAAI,KAAK;AAC5B,gBAAI,SAAS,cAAc;AACzB,kBAAI,SAAS,YAAY;AAEvB,6BAAa,KAAK,EAAE,GAAG,MAAM,QAAQ,KAAK,OAAO,CAAC;AAAA,cACpD,WAAW,SAAS,eAAe;AAEjC,gCAAgB,KAAK,EAAE,GAAG,MAAM,QAAQ,KAAK,OAAO,CAAC;AAAA,cACvD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE,GAAa;AAAA,QACb,UAAU,CAAC,MAAM,OAAO,aAAa;AACnC,cAAI,WAAW,UAAU,GAAG;AAC1B,uBAAW,KAAK,IAAI;AAAA,UACtB;AACA,cAAI,SAAS,MAAM,MAAM,KAAK,OAAO,KAAK,KAAK,UAAU,GAAG;AAC1D,mBAAiB,gBAAK,WAAW,MAAM,OAAO,QAAQ;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,aAAa,SAAS,KAAK,gBAAgB,SAAS,GAAG;AACzD,YAAM,KAAK,IAAI,YAAY,MAAM,MAAM,CAAC,CAAC;AACzC,mBAAa,QAAQ,CAAC,SAAS;AAC7B,cAAM,SAAS,KAAK;AAEpB,WAAG,OAAO,OAAO,OAAO,OAAO,KAAK,OAAO;AAAA,MAC7C,CAAC;AACD,sBAAgB,QAAQ,CAAC,SAAS;AAGhC,cAAM,YAAY,KAAK,YAAY,CAAC,GAAG,QAAQ,MAC3C,OAAO,CAAC;AACZ,WAAG,OAAO,KAAK,OAAO,UAAU,OAAO,EAAE;AACzC,WAAG,OAAO,UAAU,KAAK,KAAK,KAAK,EAAE;AAAA,MACvC,CAAC;AACD,iBAAW,QAAQ,CAAC,SAAS;AAC3B,WAAG,WAAW,KAAK,OAAO,QAAW;AAAA,MACvC,CAAC;AACD,YAAM,OAAO,GAAG,SAAS;AAAA,IAC3B;AAAA,EACF;AACF;;;ANnKA,IAAM,eAAe;AAGrB,IAAM,aAAa;AAEnB,IAAM,0BAA0B;AAEzB,IAAM,qBAAqB,CAChC,cACW;AACX,MAAI;AACJ,MAAI;AACJ,SAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM,SAAS;AACb,eAAS,MAAM,UAAU;AAAA,IAC3B;AAAA,IACA,MAAM,eAAe,gBAAgB;AACnC,mBAAa;AAAA,IACf;AAAA,IACA,MAAM,eAAe,GAAG,WAAW;AACjC,YAAM,cAA6B,CAAC;AACpC,YAAM,SAAwB,CAAC;AAC/B,aAAO,OAAO,SAAS,EAAE,QAAQ,CAAC,UAAU;AAC1C,YAAI,MAAM,QAAQ,SAAS;AACzB,cAAI,MAAM,kBAAkB,YAAY;AACtC,mBAAO,KAAK,KAAK;AAAA,UACnB;AACA,cAAI,MAAM,SAAS;AACjB,gBAAI,MAAM,kBAAkB,YAAY;AACtC,0BAAY,QAAQ,KAAK;AAAA,YAC3B,OAAO;AACL,0BAAY,KAAK,KAAK;AAAA,YACxB;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAED,YAAM,kBAAkB,YAAY;AAAA,QAClC,CAAC,MAAM,EAAE,kBAAkB;AAAA,MAC7B;AAEA,YAAM,mBAAmB,YAAY;AAAA,QACnC,CAAC,MAAM,EAAE,eAAe,SAAS;AAAA,MACnC;AAEA,YAAM,cAAc,oBAAI,IAAY;AAEpC,YAAM,gBAAgB,qBAAqB,SAAS;AAEpD,YAAM,cAAe,MAAM,MAAM;AAAA,QAC/B,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,SAAS;AAAA,UACP;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,YACT,UAAU,QAAQ,UAAU,SAAS;AACnC,kBAAI,CAAC,YAAY,QAAQ,SAAS;AAChC,uBAAO,OAAO;AAAA,cAChB;AACA,oBAAM,QAAQ,OAAO,OAAO,SAAS,EAAE;AAAA,gBACrC,CAACC,WACCA,OAAM,QAAQ,WAAW,OAAO,SAASA,OAAM,QAAQ;AAAA,cAC3D;AACA,kBAAI,OAAO;AACT,uBAAO,OAAO;AAAA,cAChB;AAAA,YACF;AAAA,YACA,MAAM,KAAK,IAAI;AACb,kBAAI,CAAC,GAAG,WAAW,IAAI,EAAG;AAE1B,kBAAI,GAAG,SAAS,YAAY,GAAG;AAC7B,uBAAO,YACJ,IAAI,CAAC,MAAM,UAAU,KAAK,UAAU,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG,EACzD,KAAK,IAAI;AAAA,cACd;AACA,oBAAM,CAAC,GAAG,KAAK,IACb,OAAO,QAAQ,SAAS,EAAE;AAAA,gBAAK,CAAC,CAACC,IAAGD,MAAK,MACvC,GAAG,SAASA,OAAM,QAAQ;AAAA,cAC5B,KAAK,CAAC;AACR,kBAAI,SAAS,MAAM,QAAQ,WAAW,GAAG;AACvC,4BAAY,IAAI,CAAC;AACjB,oBAAI,CAAC,kBAAkB;AACrB,wBAAM,KAAK;AAAA,oBACT;AAAA,oBACA;AAAA,oBACA;AAAA,kBACF;AACA,sBAAI,GAAI,QAAO;AAAA,gBACjB;AACA,uBAAO;AAAA,kBACL,MAAM,MAAM;AAAA,kBACZ,KAAK,MAAM;AAAA,gBACb;AAAA,cACF;AAAA,YACF;AAAA,YACA,eAAeC,IAAG,YAAY;AAC5B,kBAAI,kBAAkB;AACpB;AAAA,cACF;AACA,qBAAO,QAAQ,UAAU,EAAE,QAAQ,CAAC,CAACA,IAAG,KAAK,MAAM;AACjD,yCAAyB,MAAM,OAAO,aAAa;AAAA,cACrD,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,eAAe;AAAA,YACb,SAAS,QAAQ;AACf,qBAAO,UAAU,OAAO;AAAA,YAC1B;AAAA,YACA,QAAQ;AAAA,cACN,SAAS,OAAO;AAAA,YAClB;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,OAAO;AAAA,YACP,SAAS,CAAC,mBAAmB,WAAW,MAAM;AAAA,YAC9C,MAAM,mBAAmB,SAAY;AAAA,YACrC,UAAU,MAAM;AAAA,UAClB;AAAA,QACF;AAAA,MACF,CAAC;AACD,kBAAY,QAAQ,CAAC,MAAM;AACzB,YAAI,mBAAmB,UAAU,CAAC,GAAG;AACnC,iBAAO,UAAU,CAAC;AAAA,QACpB;AAAA,MACF,CAAC;AAED,YAAM,cAAc,YAAY,CAAC,EAAE,OAAO,KAAK;AAC/C,UAAI,cAAc;AAClB,UAAI,kBAAkB;AACpB,cAAM,kBAA4B,CAAC;AACnC,YAAI,YAAY;AAChB,eAAO,QAAQ,WAAW,EAAE,QAAQ,CAAC,CAACA,IAAG,KAAK,MAAM;AAClD,cAAI,MAAM,QAAQ,SAAS;AACzB,kBAAM,OAAO,KAAK,UAAU,OAAO,MAAM,QAAQ;AACjD,4BAAgB;AAAA,cACd,MAAM,KACH,UAAU,EACV,QAAQ,uBAAuB,mBAAmB,IAAI,IAAI;AAAA,YAC/D;AACA,gBAAI,MAAM,SAAS;AACjB,0BAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF,CAAC;AACD,wBAAgB,KAAK,iBAAiB,SAAS,UAAU;AACzD,sBAAc,gBAAgB,KAAK,IAAI;AACvC,cAAM,gBAAgB,MAAM,KAAK,KAAK,aAAa,CAAC,EAAE;AAAA,UACpD,CAAC,MAAM,KAAK,OAAO;AAAA,QACrB;AAEA,cAAM,aAAa,cAAc;AAAA,UAC/B,CAAC,GAA2B,MAAM;AAChC,cAAE,CAAC,IAAI,GAAG,uBAAuB,IAAI,CAAC;AACtC,mBAAO;AAAA,UACT;AAAA,UACA,CAAC;AAAA,QACH;AAEA,sBAAc;AAAA,UACZ,OAAO,KAAK,UAAU,EAAE,SAAS,IAC7B,kCAAkC,KAAK,UAAU,UAAU,CAAC,SAC5D;AAAA,UACJ,GAAG,cAAc;AAAA,YACf,CAAC,OACC,cAAc,KAAK;AAAA,cACjB,GAAG,uBAAuB,IAAI,EAAE;AAAA,YAClC,CAAC,KAAK;AAAA,cACJ,OAAO,mBAAmB,EAAE;AAAA,YAC9B,CAAC;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT,EACG,OAAO,CAAC,MAAM,CAAC,EACf,KAAK,IAAI;AAEZ,YAAI,OAAO,OAAO,YAAY,YAAY;AACxC,iBAAO,mBAAmB;AAAA,YACxB,GAAG,uBAAuB,OAAO,QAAQ;AAAA,UAC3C;AAAA,QACF,OAAO;AACL,yBACG,MAAM,iBAAiB,GAAG,KAAK,IAAI,IAAI,OAAO;AAAA,QACnD;AAAA,MACF,OAAO;AAEL,eAAO,QAAQ,WAAW,EAAE,QAAQ,CAAC,CAACA,IAAG,KAAK,MAAM;AAClD,cAAI,MAAM,QAAQ,WAAW,MAAM,SAAS;AAC1C,0BAAc,MAAM;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,gBAAgB,MAAM,iBAAiB,QAAQ,SAAS;AAE9D,UAAI;AACJ,UAAI,OAAO,MAAM,WAAW;AAC1B,0BAAkB;AAAA,UAChB;AAAA,UACA;AAAA,UACA;AAAA,UACA,WAAW,MAAM,WAAW;AAAA,QAC9B;AAAA,MACF,OAAO;AACL,0BAAkB,oBAAI,IAAY;AAAA,MACpC;AAEA,YAAM,UAAU,MAAM;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,aAAa,CAAC,SAAS,eAAe,WAAW,EACpD,OAAO,CAAC,MAAM,CAAC,EACf,KAAK;AAAA;AAAA,CAAM,EACX,QAAQ;AACX,UAAI,iBAAiB;AACnB,wBAAgB,WAAW,OAAO,MAAM;AACxC,wBAAgB,OAAO;AAAA,MACzB,OAAO;AACL,aAAK,SAAS;AAAA,UACZ,MAAM;AAAA,UACN,UAAU,OAAO,MAAM;AAAA,UACvB,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAEA,UAAI,OAAO,MAAM,cAAc;AAC7B,aAAK,SAAS;AAAA,UACZ,MAAM;AAAA,UACN,UAAU,OAAO,MAAM,aAAa;AAAA,UACpC,QAAQ,MAAM;AAAA,YACZ;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;;;AOxQO,IAAM,gBAAgB,CAC3B,cACW;AACX,MAAI;AACJ,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM,OAAO,YAAY;AACvB,eAAS,MAAM,UAAU;AACzB,aAAO;AAAA,QACL,SAAS;AAAA,UACP,OAAO;AAAA,YACL,CAAC,OAAO,WAAW,GAAG;AAAA,UACxB;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,WAAW;AAAA,YACT,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,mBAAmB,OAAO;AAAA,UAC1B,uBAAuB,OAAO;AAAA,UAC9B,eAAe;AAAA,UACf,WAAW;AAAA,UACX,cAAc;AAAA,UACd,QAAQ,WAAW,OAAO,UAAU;AAAA,UACpC,WAAW,WAAW,OAAO,aAAa;AAAA,UAC1C,WAAW;AAAA,UACX,eAAe;AAAA,YACb,OAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACtCA,SAAS,iBAAAC,sBAAqB;;;ACA9B,OAAOC,SAAQ;AACf,OAAOC,WAAU;AACjB,SAAS,qBAAqB;AAyBvB,IAAM,gBAAgB,YAAkC;AAC7D,QAAM,SAAqC,MAAMC,IAC9C,SAASC,MAAK,QAAQ,QAAQ,IAAI,GAAG,cAAc,GAAG,OAAO,EAC7D,KAAK,KAAK,KAAK,EACf,MAAM,MAAM;AAAA,EAAC,CAAC;AAEjB,QAAM,MAAmB,CAAC;AAC1B,MAAI,CAAC,OAAQ,QAAO;AACpB,SAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACzC,QAAI,OAAO,KAAK,UAAU;AACxB,cAAQ,IAAI,KAAK,GAAG,CAAC;AAAA,IACvB;AAAA,EACF,CAAC;AACD,MACE,OAAO,OAAO,WAAW,YACzB,OAAO,OAAO,QAAQ,QAAQ,UAC9B;AACA,QAAI,SAAS,OAAO,OAAO;AAAA,EAC7B;AACA,MAAI,OAAO,OAAO,SAAS,YAAY,OAAO,OAAO,MAAM,OAAO,UAAU;AAC1E,QAAI,OAAO,OAAO,KAAK;AAAA,EACzB;AACA,MACE,OAAO,OAAO,eAAe,YAC7B,OAAO,OAAO,YAAY,OAAO,UACjC;AACA,UAAM,EAAE,IAAI,IAAI,OAAO;AACvB,QAAI,IAAI,WAAW,MAAM,GAAG;AAC1B,UAAI,aAAa;AAAA,IACnB,WAAW,IAAI,WAAW,UAAU,GAAG;AACrC,UAAI,aAAa,IAAI,UAAU,CAAC;AAAA,IAClC;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,aAAa,CAAC,SAA0B,KAAK,WAAW,GAAG;AACjE,IAAM,wBAAwB,OAC5B,YACgC;AAChC,QAAM,IAAI,cAAc,QAAQ,IAAI,CAAC,EAAE,MAAM,GAAG;AAChD,WAAS,IAAI,EAAE,QAAQ,IAAI,GAAG,KAAK;AACjC,UAAM,KAAK,GAAG,EAAE,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,iBAAiB,OAAO;AAC7D,QAAI,MAAM,UAAU,EAAE,GAAG;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,IAAM,0BAA0B,OAAO,OAAgC;AACrE,MAAI;AACF,WAAO,cAAc,EAAE;AAAA,EACzB,SAAS,GAAG;AAGV,UAAM,IAAI,MAAM,sBAAsB,EAAE;AACxC,QAAI,CAAC,GAAG;AACN,YAAM;AAAA,IACR;AACA,WAAO;AAAA,EACT;AACF;AAEO,IAAM,oBAAoB,OAAO,eAAuB;AAC7D,QAAM,cAAc,cAAc,WAAW,MAAM,GAAG,EAAE,CAAC,CAAC;AAC1D,QAAM,cAAc,OAAO,YAAY;AACrC,UAAM,IAAI,cAAc,MAAM,wBAAwB,WAAW,CAAC,EAAE;AAAA,MAClE;AAAA,MACA;AAAA,IACF;AACA,QAAI,WAAW,UAAU,GAAG;AAC1B,aAAO,EAAE,MAAM,GAAG,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,IACvC;AACA,WAAO;AAAA,EACT,GAAG;AACH,MAAI,UAA8B;AAClC,QAAM,WAAW,YAAY,MAAM,GAAG;AACtC,MAAI,OAAO;AACX,SAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,SAAS,KAAK,GAAG;AACxB,UAAM,WAAW,OAAO,YAAY;AAClC,YAAM,IAAI,MAAM,sBAAsB,GAAG,IAAI,eAAe;AAC5D,UAAI,GAAG;AACL,eAAO;AAAA,MACT;AACA,UAAI;AACF,eAAO,cAAc,GAAG,IAAI,eAAe;AAAA,MAC7C,QAAQ;AACN,eAAO;AAAA,MACT;AAAA,IACF,GAAG;AACH,QAAI,aAAa,UAAa,CAAE,MAAM,UAAU,QAAQ,GAAI;AAC1D,eAAS,IAAI;AACb;AAAA,IACF;AACA,UAAM,aAA0B,KAAK;AAAA,MACnC,MAAMD,IAAG,SAAS,UAAU,OAAO;AAAA,IACrC;AACA,cAAU,WAAW;AACrB;AAAA,EACF;AACA,MAAI,YAAY,QAAW;AACzB,YAAQ;AAAA,MACN,oCAAoC,WAAW,iBAAiB,WAAW;AAAA,IAC7E;AACA,WAAO;AACP,cAAU;AAAA,EACZ;AACA,SAAO,EAAE,SAAS,MAAM,YAAY;AACtC;;;ADnIO,IAAM,yBAAyB,CACpC,cACW;AACX,MAAI;AACJ,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM,SAAS;AACb,eAAS,MAAM,UAAU;AACzB,iBAAW,CAAC,YAAY,cAAc,KAAK,OAAO,MAAM,iBAAiB;AACvE,cAAM,EAAE,MAAM,QAAQ,IAAI,MAAM,kBAAkB,UAAU;AAC5D,YAAI,OAAO,kBAAkB,UAAU;AACrC,iBAAO,mBAAmB,UAAU,IAAI;AAAA,QAC1C,WAAW,OAAO,kBAAkB,YAAY;AAC9C,iBAAO,mBAAmB,UAAU,IAAI,MAAM;AAAA,YAC5C;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,WAAW,0BAA0B,OAAO;AAC1C,gBAAM,CAAC,SAAS,GAAG,UAAU,IAAI;AACjC,cAAI,OAAO,WAAW,UAAU;AAC9B,mBAAO,mBAAmB,UAAU,IAAI;AAAA,UAC1C,WAAW,OAAO,WAAW,YAAY;AACvC,mBAAO,mBAAmB,UAAU,IAAI,MAAM;AAAA,cAC5C;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AACA,qBAAW,UAAU,YAAY;AAE/B,gBAAI,OAAO,UAAU,UAAU;AAC7B,qBAAO,eAAe,KAAK,EAAE,KAAK,QAAQ,WAAW,CAAC;AAAA,YACxD,WAAW,OAAO,UAAU,YAAY;AACtC,qBAAO,eAAe,KAAK;AAAA,gBACzB,KAAK,MAAM,OAAO,SAAS,MAAM,UAAU;AAAA,gBAC3C;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,OAAO;AAAA,UACL,eAAe;AAAA,YACb,SAAS,QAAQ,WAAW,aAAa;AACvC,qBAAO,UAAU,OAAO;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,MAAM,iBAAiB;AACrB,YAAM,eAAe,IAAI;AAAA,QACvB,MAAM,KAAK,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,MAAME,eAAc,CAAC,CAAC;AAAA,MAC7D;AACA,aAAO,qBAAqB,OAAO,eAChC,OAAO,CAAC,MAAM,aAAa,IAAI,EAAE,UAAU,CAAC,EAC5C,IAAI,CAAC,MAAM,EAAE,GAAG;AAAA,IACrB;AAAA,EACF;AACF;;;AEjEA,IAAM,YAAY,CAAC,SAAiB;AAElC,QAAM,MAAM,mBAAmB,IAAI;AAEnC,cAAY,GAAG;AACf,SAAO;AACT;AAEA,IAAM,aAAa,CAAC;AAAA;AAAA,EAElB,KAAK,MAAM,mBAAmB,IAAI,CAAC;AAAA;AAErC,IAAM,YAAY,CAAC,MAAc;AAAA;AAAA,EAE/B,kBAAkB,MAAM,KAAK,EAAE;AAAA,IAC7B;AAAA,IACA,QAAQ,IAAI;AAAA,EACd;AAAA;AAEF,IAAM,YAAY,CAAC;AAAA;AAAA,EAEjB,mBAAmB,IAAI;AAAA;AAEzB,IAAM,aAAa;AAAA,EACjB,4BAA4B,SAAS;AAAA,EACrC,6BAA6B,UAAU;AAAA,EACvC,4BAA4B,SAAS;AAAA,EACrC,4BAA4B,SAAS;AACvC,EAAE,KAAK,GAAG;AAEH,IAAM,wBAAwB,MAAc;AACjD,SAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM,UAAU,IAAI;AAClB,UAAI,MAAM,gCAAgC;AACxC,eAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA,MAAM,KAAK,IAAI;AACb,UAAI,MAAM,kCAAkC;AAC1C,eAAO,SAAS,YAAY,IAAI;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AACF;;;AChDA,SAAS,iBAAAC,sBAAqB;AAM9B,IAAM,uBAAuB;AAEtB,IAAM,yBAAyB,CACpC,cACW;AACX,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,iBAGF,CAAC;AACL,SAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM,SAAS;AACb,eAAS,MAAM,UAAU;AACzB,eAAS,MAAM,OAAO,QAAQ;AAAA,IAChC;AAAA,IACA,eAAe,QAAQ;AACrB,mBAAa;AAAA,IACf;AAAA,IACA,MAAM,UAAU,IAAI;AAClB,YAAM,EAAE,iBAAiB,IAAI,OAAO;AACpC,UAAI,MAAM,kBAAkB;AAC1B,eAAO,uBAAuB,KAAK;AAAA,MACrC;AAEA,YAAM,CAAC,UAAU,KAAK,IAAI,GAAG,MAAM,KAAK,CAAC;AACzC,UAAI,SAAS,SAAS,MAAM,KAAK,OAAO;AACtC,cAAM,MAAM;AAAA,UACV;AAAA,UACA;AAAA,UACA,MACG,MAAM,GAAG,EACT,OAAO,CAAC,MAAM,KAAK,MAAM,EACzB,KAAK,GAAG;AAAA,QACb,EAAE,KAAK,EAAE;AACT,YAAI,OAAO,kBAAkB;AAC3B,iBAAO,uBAAuB,MAAM;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAAA,IACA,MAAM,KAAK,IAAI;AACb,UAAI,GAAG,WAAW,oBAAoB,KAAK,GAAG,SAAS,IAAI,GAAG;AAC5D,cAAM,EAAE,iBAAiB,IAAI,OAAO;AACpC,cAAM,aAAa,GAAG;AAAA,UACpB,qBAAqB;AAAA,UACrB,GAAG,SAAS;AAAA,QACd;AACA,YAAI,EAAE,cAAc,mBAAmB;AACrC;AAAA,QACF;AACA,cAAM,MAAM,MAAM,kBAAkB,UAAU;AAC9C,cAAM;AAAA,UACJ,cAAc;AAAA,UACd,aAAa;AAAA,UACb;AAAA,UACA;AAAA,QACF,IAAI,iBAAiB,UAAU;AAC/B,cAAM,eAAe,MAAM,eAAe,EAAE,GAAG,KAAK,WAAW,CAAC;AAChE,cAAM,cAAc,MAAM,cAAc,EAAE,GAAG,KAAK,WAAW,CAAC;AAC9D,uBAAe,UAAU,IAAI;AAAA,UAC3B;AAAA,UACA;AAAA,QACF;AAEA,YAAI,YAAY;AACd,iBAAO;AAAA,YACL,MAAM,WAAW;AAAA,cACf,GAAG;AAAA,cACH;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,WAAW,QAAQ;AACjB,cAAI;AACJ,cACE,OAAO;AAAA,UACP,OAAO,KAAK,SAAS,KACrB,OAAO,QAAQ,YACf;AACA,gBAAI,QAAQ,IAAI,QAAQ,OAAO,WAAW,KAAK,iBAAiB;AAC9D,uBAAS,OACN,SAAS,EACT,QAAQ,eAAe,iBAAiB;AAAA,YAC7C,OAAO;AACL,uBAAS,OAAO,SAAS,EAAE,QAAQ,eAAe,WAAW;AAAA,YAC/D;AAAA,UACF,OAAO;AACL,qBAAS,OAAO,SAAS;AAAA,UAC3B;AACA,iBAAO;AAAA,YACL,mBAAmB,MAAM,KAAK,KAAK,UAAU;AAAA,cAC3C;AAAA,cACA;AAAA,cACA,GAAG;AAAA,YACL,CAAe,CAAC;AAAA,UAClB;AAAA,QACF;AAEA,YAAI,aAAiC;AACrC,cAAM,CAAC,UAAU,KAAK,IAAI,WAAW,MAAM,KAAK,CAAC;AACjD,cAAM,MAAM,SAAS,MAAM,GAAG,EAAE,IAAI;AACpC,cAAM,WAAW,OAAO,OAAO,GAAG,KAAK;AACvC,cAAM,YAAY,IAAI,gBAAgB,KAAK;AAC3C,YAAI,UAAU,IAAI,KAAK,KAAK,UAAU,IAAI,QAAQ,GAAG;AACnD,uBAAa;AAAA,YACX;AAAA,YACA,4BAA4B,KAAK,UAAU;AAAA,cACzC;AAAA,cACA;AAAA,YACF,CAAC,CAAC;AAAA,UACJ,EAAE,KAAK,GAAG;AAAA,QACZ,WAAW,UAAU,IAAI,KAAK,GAAG;AAC/B,uBAAa;AAAA,YACX;AAAA,YACA,4BAA4B,KAAK,UAAU,CAAC,YAAY,CAAC,CAAC;AAAA,UAC5D,EAAE,KAAK,GAAG;AAAA,QACZ,WAAW,OAAO,QAAQ;AAExB,uBAAa;AAAA,YACX;AAAA,YACA,4BAA4B,KAAK,UAAU,CAAC,YAAY,CAAC,CAAC;AAAA,UAC5D,EAAE,KAAK,GAAG;AAAA,QACZ,WAAW,OAAO,OAAO;AACvB,uBAAa;AAAA,YACX;AAAA,YACA,4BAA4B,KAAK,UAAU,CAAC,YAAY,CAAC,CAAC;AAAA,UAC5D,EAAE,KAAK,GAAG;AAAA,QACZ,WAAW,WAAW,cAAc,WAAW,MAAM,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG;AAChE,gBAAM,YAAY,OAAO,MAAM,GAAG;AAClC,uBAAa;AAAA,YACX;AAAA,YACA,4BAA4B,KAAK,UAAU;AAAA,cACzC;AAAA,cACA;AAAA,YACF,CAAC,CAAC;AAAA,UACJ,EAAE,KAAK,GAAG;AAAA,QACZ;AACA,YAAI,YAAY;AACd,cACE,WAAW,SAAS,WAAW,KAC/B,WAAW,SAAS,YAAY,KAChC,WAAW,SAAS,WAAW,GAC/B;AACA,mBAAO,WAAW,MAAM,IAAI,oBAAoB;AAAA,UAClD,WAAW,WAAW,SAAS,WAAW,GAAG;AAC3C,mBAAO,WAAW,MAAM,IAAI,mBAAmB;AAAA,UACjD;AACA,iBAAO,SAAS,UAAU;AAAA,QAC5B;AAEA,cAAM,IAAI,MAAM,WAAW,UAAU,mBAAmB;AAAA,MAC1D;AAAA,IACF;AAAA,IACA,iBAAiB;AACf,YAAM,eAAe,IAAI;AAAA,QACvB,MAAM,KAAK,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,MAAMC,eAAc,CAAC,CAAC;AAAA,MAC7D;AACA,YAAM,KAAK,YAAY,EAAE,QAAQ,CAAC,OAAO;AACvC,YAAI,GAAG,WAAW,oBAAoB,KAAK,GAAG,SAAS,IAAI,GAAG;AAC5D,uBAAa;AAAA,YACX,GAAG,UAAU,qBAAqB,QAAQ,GAAG,SAAS,CAAC;AAAA,UACzD;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,kBAA0C,CAAC;AACjD,aAAO,QAAQ,cAAc,EAAE;AAAA,QAC7B,CAAC,CAAC,YAAY,EAAE,cAAc,YAAY,CAAC,MAAM;AAC/C,cAAI,aAAa,IAAI,UAAU,GAAG;AAChC,4BAAgB,YAAY,IAAI;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AACA,aAAO,kBAAkB;AAAA,IAC3B;AAAA,EACF;AACF;;;ACpLO,IAAM,qBAAqB,MAAc;AAC9C,MAAI;AACJ,SAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM,eAAe,gBAAgB;AACnC,mBAAa;AAAA,IACf;AAAA,IACA,MAAM,UAAU,MAAM,IAAI;AACxB,YAAM,CAAC,GAAG,QAAQ,KAAK,IAAI,GAAG,MAAM,KAAK,CAAC;AAC1C,WACG,MAAM,MAAM,GAAG,EAAE,SAAS,KAAK,KAAK,WAAW,cAAc,EAAE,MAChE,KAAK,MAAM,sBAAsB,GACjC;AACA,cAAM,MAAM,KAAK,MAAM,IAAI;AAC3B,cAAM,cAAc,IAAI,KAAK,CAAC;AAC9B,YAAI,aAAa,QAAQ,4BAA4B;AACnD,gBAAM,YAAY,aAAa;AAC/B,cACE,WAAW,QAAQ,aACnB,OAAO,UAAU,SAAS,YAC1B,UAAU,MAAM,CAAC,MAAM,KACvB;AACA,kBAAM,KAAK,KAAK,UAAU,UAAU,KAAK;AACzC,mBAAO,0BAA0B,EAAE;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACjCO,IAAM,mBAAmB,MAAc;AAC5C,SAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM,UAAU,MAAM,IAAI;AACxB,UAAI,GAAG,SAAS,0CAA0C,GAAG;AAG3D,eAAO,KAAK;AAAA,UACV;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACfO,IAAM,mBAAmB,MAAc;AAE5C,SAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM,SAAS;AACb,YAAM,WAAW,MAAM,OAAO,aAAa,GAAG;AAC9C,aAAO;AAAA,QACL,KAAK;AAAA,UACH,SAAS;AAAA,YACP,SAAS,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,CAAC;AAAA,UACtC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AClBA,OAAOC,WAAU;AAEjB,SAAS,iBAAAC,sBAAqB;;;ACA9B,IAAM;AAAA;AAAA,EAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY1B,UAAU;AAAA;AAEL,IAAM,WAAW,CACtB,OACG,SACA;AACH,SAAO,SAAS,QAAQ,YAAY,kBAAkB,IAAI,GAAG,IAAI,CAAC;AACpE;AAEO,IAAM,iBAAiB,CAAC,aAAqB;AAIlD,SAAO;AAEP,QAAM,MAAM,oBAAoB,IAAI,IAAI,QAAQ,EAAE;AAElD,WAAS,GAAG,IAAI;AAChB,UAAQ,IAAI,qDAAqD;AAEjE,MAAI,OAAO,kBAAkB,YAAY;AAEvC,kBAAc,SAAS,MAAM,UAAU;AAAA,MACrC,MAAM;AAAA,MACN,KAAK;AAAA,IACP,CAAC;AAAA,EACH,OAAO;AACL,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,OAAO;AAEd,QAAI,OAAO,cAAc;AAGvB,YAAM,SAAS,OAAO,aAAa,aAAa,KAAK;AAAA,QACnD,iBAAiB,CAAC,UAAmB;AAAA,MACvC,CAAC;AACD,YAAM,mBAAmB,OAAO,gBAAgB,QAAQ;AACxD,aAAO,MAAM;AAAA,IACf,OAAO;AACL,aAAO,MAAM;AAAA,IACf;AACA,aAAS,KAAK,OAAO,MAAM;AAAA,EAC7B;AACA,UAAQ,IAAI,0DAA0D;AACxE;AAEO,IAAM,eAAe,CAAC,MAAkB,WAAqB,CAAC,MAAM;AACzE,QAAM,MAAM,oBAAoB,IAAI,IAAI,KAAK,GAAG,EAAE;AAElD,QAAM,eAAuB,SAAS,GAAG;AACzC,MAAI,CAAC,cAAc;AACjB,YAAQ,IAAI,6CAA6C;AACzD;AAAA,EACF;AAGA,SAAO,eAAe;AACtB,UAAQ,IAAI,yDAAyD;AAErE,WAAS,KAAK,IAAI;AAClB,MAAI,iBAAiB;AACrB,WAAS,QAAQ,CAAC,YAAY;AAE5B,UAAM,KAAK,aAAa,OAAO;AAC/B,QAAI,IAAI;AAEN,aAAO,OAAO,IAAI,aAAa,OAAO;AACtC;AAAA,IACF;AAAA,EACF,CAAC;AACD,UAAQ;AAAA,IACN,8BAA8B,cAAc,IAAI,SAAS,MAAM;AAAA,EACjE;AACF;AAEO,IAAM,sBAAsB,OAAO,QAAgB;AACxD,QAAM,QAAQ,CAAC,IAAI,MAAM,IAAI,QAAc,CAAC,QAAQ,WAAW,KAAK,CAAC,CAAC;AACtE,QAAM,MAAM;AACZ,QAAM,IAAI,IAAI,IAAI,KAAK,SAAS,MAAM;AACtC,IAAE,aAAa,IAAI,UAAU,EAAE,MAAM;AACrC,MAAI,UAAU,OAAO,QAAQ;AAC3B,aAAS,OAAO,EAAE;AAClB,UAAM,MAAM,GAAG;AACf,WAAO,MAAM;AACb;AAAA,EACF;AAEA,QAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,WAAS,KAAK,OAAO,KAAK;AAC1B,QAAM;AAAA,EAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmB5B,KAAK;AACL,WAAS,KAAK;AAAA,EAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMnC,KAAK;AACP,QAAM,MAAM;AACZ,QAAM,IAAI,SAAS,cAAc,GAAG;AACpC,IAAE,OAAO,SAAS;AAClB,IAAE,OAAO,SAAS;AACpB;AAEO,IAAM,kBAAkB,OAAO,SAAmB;AACvD,QAAM,QAAQ,CAAC,IAAI,MAAM,IAAI,QAAc,CAAC,QAAQ,WAAW,KAAK,CAAC,CAAC;AACtE,QAAM,MAAM;AACZ,QAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,WAAS,KAAK,OAAO,KAAK;AAC1B,QAAM;AAAA,EAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+B5B,KAAK;AACL,MAAI,UAAU,OAAO,UAAU,KAAK,UAAU,GAAG;AAC/C,UAAM,IAAI,IAAI,IAAI,KAAK,CAAC,GAAG,SAAS,MAAM;AAC1C,aAAS,OAAO,EAAE;AAClB,UAAM,MAAM,GAAG;AACf,WAAO,MAAM;AACb;AAAA,EACF,WAAW,KAAK,UAAU,GAAG;AAC3B,aAAS,KAAK;AAAA,IAAuB;AAAA;AAAA;AAAA;AAAA,MAInC,KAAK;AACP;AAAA,EACF,OAAO;AACL,aAAS,KAAK;AAAA,IAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWnC,KAAK;AACP,UAAM,MAAM;AACZ,UAAM,QAAQ,SAAS,cAA2B,OAAO;AACzD,SAAK,KAAK,EAAE,QAAQ,CAAC,GAAG,UAAU;AAChC,YAAM,KAAK,SAAS,cAAc,IAAI;AACtC,YAAM,MAAM,SAAS,cAAc,IAAI;AACvC,YAAM,MAAM,SAAS,cAAc,IAAI;AACvC,YAAM,IAAI,SAAS,cAAc,GAAG;AACpC,UAAI,YAAY,GAAG,QAAQ,CAAC;AAC5B,UAAI,UAAU,OAAO,QAAQ;AAC3B,UAAE,SAAS;AAAA,MACb;AACA,QAAE,OAAO;AACT,QAAE,cAAc,IAAI,IAAI,GAAG,SAAS,MAAM,EAAE;AAC5C,UAAI,OAAO,CAAC;AACZ,SAAG,OAAO,GAAG;AACb,SAAG,OAAO,GAAG;AACb,YAAM,OAAO,EAAE;AAAA,IACjB,CAAC;AAAA,EACH;AACF;;;ADpNO,IAAM,iBAAiB,MAAc;AAC1C,MAAI;AACJ,SAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,eAAe,QAAQ;AACrB,mBAAa;AAAA,IACf;AAAA,IACA,MAAM,uBAAuB,QAAQ;AACnC,aAAO,YAAY,IAAI,OAAO,KAAK,KAAK,SAAS;AAC/C,YAAI,CAAC,KAAK,aAAa,EAAE,UAAU,IAAI,OAAO,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG;AAChE,gBAAM,cAAcC,MAAK,KAAK,QAAQ,IAAI,GAAG,WAAW,MAAM,MAAM;AACpE,gBAAM,OAAiB,CAAC;AACxB,2BAAiB,YAAY,KAAK,WAAW,GAAG;AAC9C,gBAAI,SAAS,SAAS,UAAU,GAAG;AACjC,oBAAM,WAAWC;AAAA,gBACfD,MAAK,SAAS,aAAa,QAAQ;AAAA,cACrC;AACA,mBAAK,KAAK,MAAM,QAAQ;AAAA,YAC1B;AAAA,UACF;AACA,cAAI,UAAU,gBAAgB,0BAA0B;AACxD,cAAI,IAAI,SAAS,iBAAiB,IAAI,CAAC;AACvC;AAAA,QACF;AACA,aAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;AE/BA,IAAM,iBAAiB;AACvB,IAAM,WAAW,OAAO;AACjB,IAAM,wBAAwB,MAAc;AACjD,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU,QAAQ;AAChB,UAAI,WAAW,gBAAgB;AAC7B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,KAAK,IAAI;AACP,UAAI,MAAM,UAAU;AAClB,cAAM,cAAc,CAAC,MAAM,GAAG,eAAe,cAAc;AAE3D,cAAM,eAAe,YAClB,IAAI,CAAC,MAAM;AACV,iBAAO,QAAQ,CAAC,oCAAoC,CAAC,qBAAqB,CAAC;AAAA,QAC7E,CAAC,EACA,OAAO,uDAAuD;AACjE,cAAM,oBAAoB,YAAY,OAAO,cAAc;AAC3D,eACE,aAAa,KAAK,IAAI,IACtB;AAAA,UAAa,kBACV,IAAI,CAAC,MAAM,MAAM,CAAC,OAAO,CAAC,GAAG,EAC7B,KAAK,IAAI,CAAC;AAAA,MAEjB;AAAA,IACF;AAAA,EACF;AACF;;;ACnCA,OAAOE,SAAQ;AAEf,OAAOC,WAAU;AAEjB,SAAS,iBAAAC,sBAAqB;;;ACJ9B,OAAOC,SAAQ,YAAY;AAC3B,SAAS,YAAY;AAErB,OAAO,UAAU;AAEjB,OAAO,WAAW;AAClB,OAAO,YAAY;AAGnB,IAAM,mBAAmBC,MAAK,QAAQ,cAAc,mBAAmB,CAAC;AAOjE,SAAS,YAAY,KAAa,KAAoB;AAG3D,QAAM,UAAU,OAAO,QAAQ,WAAW,MAAM,QAAQ,IAAI,WAAW;AACvE,MAAI,QAAQ,YAAY,EAAE,SAAS,KAAK,GAAG;AACzC,sBAAkB,SAAS,GAAG;AAAA,EAChC,WAAW,QAAQ,YAAY,MAAM,QAAQ;AAC3C,UAAM,cAAc,QAAQ,IAAI,eAC5B,QAAQ,IAAI,aAAa,MAAM,GAAG,IAClC,CAAC;AACL,wBAAoB,SAAS,aAAa,GAAG;AAAA,EAC/C;AACF;AAEA,SAAS,kBAAkB,YAAoB,KAAa;AAC1D,QAAM,YAAY,QAAQ,KAAK,MAAM,CAAC;AACtC,QAAM,QAAQ,MAAM,QAAQ,UAAU,CAAC,YAAY,GAAG,WAAW,GAAG,GAAG;AAAA,IACrE,OAAO;AAAA,EACT,CAAC;AACD,QAAM,GAAG,SAAS,CAAC,SAAS;AAC1B,QAAI,SAAS,GAAG;AACd,cAAQ;AAAA,QACN,qBACE,OAAO;AAAA,UACL;AAAA;AAAA;AAAA,EAAqE,OAAO;AAAA,YAC1E;AAAA,UACF,CAAC,qBAAqB,IAAI;AAAA,QAC5B;AAAA,QACF,EAAE,OAAO,KAAK;AAAA,MAChB;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,IAAM,4BAA4B;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,eAAe,oBACb,SACA,aACA,KACA;AAKA,QAAM,sBACJ,YAAY,kBAAkB,kBAAkB;AAClD,QAAM,qCACJ,QAAQ,aAAa,aACpB,CAAC,uBACA,0BAA0B,SAAS,mBAAmB;AAE1D,MAAI,oCAAoC;AACtC,QAAI;AACF,YAAM,KAAK,MAAM,UAAU,QAAQ;AACnC,YAAM,gBACJ,uBAAuB,GAAG,SAAS,mBAAmB,IAClD,sBACA,0BAA0B,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;AAC1D,UAAI,eAAe;AAEjB,cAAM;AAAA,UACJ,qCAAqC,GAAG,MAAM,aAAa;AAAA,UAC3D;AAAA,YACE,KAAK,KAAK,kBAAkB,KAAK;AAAA,UACnC;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF,QAAQ;AAAA,IAER;AAAA,EACF;AAMA,MAAI,QAAQ,aAAa,YAAY,YAAY,QAAQ;AACvD,cAAU;AAAA,EACZ;AAIA,MAAI;AACF,UAAM,UAAmB,UACrB,EAAE,KAAK,EAAE,MAAM,SAAS,WAAW,YAAY,EAAE,IACjD,CAAC;AAEL,QAAI,QAAQ,CAAC,GAAG,WAAW;AACzB,WAAK,KAAK,OAAO,EACd,KAAK,CAAC,eAAe;AACpB,mBAAW,GAAG,SAAS,MAAM;AAAA,MAC/B,CAAC,EACA,MAAM,MAAM;AAAA,IACjB,CAAC,EAAE,MAAM,CAAC,QAAQ;AAChB,cAAQ,MAAM,sBAAsB,IAAI,SAAS,IAAI,QAAQ;AAAA,IAC/D,CAAC;AAED,WAAO;AAAA,EACT,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEA,SAAS,UAAU,SAAiB,SAAwC;AAC1E,SAAO,IAAI,QAAQ,CAACC,UAAS,WAAW;AACtC,SAAK,SAAS,SAAS,CAAC,OAAO,WAAW;AACxC,UAAI,OAAO;AACT,eAAO,KAAK;AAAA,MACd,OAAO;AACL,QAAAA,SAAQ,OAAO,SAAS,CAAC;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;;;ADzHA,IAAM,YAAY;AACX,IAAM,kBAAkB,YAAY;AAC3C,IAAM,YAAY,YAAY;AAC9B,IAAM,YAAY,YAAY;AAC9B,IAAM,WAAW,YAAY;AAE7B,IAAM,kBAAkB;AACxB,IAAM,YAAY;AAClB,IAAM,cAAc,UAAU,SAAS;AACvC,IAAM,kBAAkB;AAEjB,IAAM,gBAAgB,CAC3B,cACW;AACX,MAAI;AACJ,MAAI;AACJ,SAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM,OAAO,YAAY;AACvB,eAAS,MAAM,UAAU;AACzB,aAAO;AAAA,QACL,SAAS;AAAA,UACP,MAAM,WAAW,SAAS,QAAQ;AAAA,UAClC,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,UACN,MAAM,WAAW,QAAQ,QAAQ;AAAA,UACjC,MAAM,WAAW,QAAQ,QAAQ,OAAO,OAAO;AAAA,UAC/C,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,IACA,MAAM,eAAe,gBAAgB;AACnC,mBAAa;AAAA,IACf;AAAA,IACA,MAAM,gBAAgB,QAAQ;AAC5B,iBAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,OAAO,WAAW,IAAI,GAAG;AAC3D,gBAAQ,IAAI,OAAO,WAAW,MAAM,GAAG,OAAO,OAAO,OAAO,CAAC,CAAC;AAAA,MAChE;AAGA,aAAO,WAAW,MAAM,IAAI,GAAG;AAG/B,aAAO,YAAY,IAAI,CAAC,GAAG,KAAK,SAAS;AACvC,cAAM,OAAO;AACb,YAAI,CAAC,IAAI,UAAU,IAAI,GAAG;AACxB,cAAI,UAAU,MAAM,MAAM;AAAA,QAC5B;AACA,aAAK;AAAA,MACP,CAAC;AAED,YAAM,kBAAkB,CAAC,QAAwB;AAC/C,YAAI,UAAU,+BAA+B,GAAG;AAChD,YAAI,UAAU,gBAAgB,wBAAwB;AAAA,MACxD;AAEA,aAAO,YAAY,IAAI,OAAO,KAAK,KAAK,SAAS;AAC/C,YAAI,EAAE,IAAI,WAAW,SAAS,IAAI,OAAO,IAAI,IAAI,WAAW,SAAS;AACnE,iBAAO,KAAK;AACd,cAAM,MAAM,IAAI,IAAI,IAAI,KAAK,WAAW;AACxC,YAAI,IAAI,aAAa,iBAAiB;AACpC,0BAAgB,GAAG;AACnB,gBAAM,SACJ,QAAQ,IAAI,aAAa,IAAI,QAAQ,CAAC,GAAG,WACxC,MAAM;AACL,kBAAM,OAAQ,kBAAC,MAAM;AACnB,qBAAO,OAAO,MAAM,WAChB,MAAM,YACJ,IACA,YACF;AAAA,YACN,GAAG,WAAW,OAAO,IAAI;AACzB,mBAAO,GAAG,WAAW,OAAO,QAAQ,UAAU,MAAM,MAAM,IAAI,IAAI,WAAW,OAAO,IAAI;AAAA,UAC1F,GAAG;AACL,kBAAQ,IAAI,YAAY,iBAAiB,MAAM;AAC/C,cAAI;AAAA,YACF;AAAA,cACE,MAAM,2BAA2B,QAAQ,oBAAI,IAAI,GAAG,OAAO;AAAA,cAC3D;AAAA,gBACE;AAAA,gBACA,IAAI,IAAI,WAAW,MAAM,EAAE;AAAA,cAC7B;AAAA,cACA;AAAA,YACF,EAAE,KAAK,MAAM;AAAA,UACf;AAAA,QACF,WAAW,IAAI,aAAa,WAAW;AACrC,0BAAgB,GAAG;AACnB,gBAAM,UAAU,MAAM,OACnB,mBAAmB,KAAK,iBAAiB,IAAI,WAAW,EACxD,KAAK,sBAAsB;AAC9B,gBAAM,aACJ,OAAO,OAAO,aAAa,CAAC,SAAS,IAAI,CAAC,GAC1C;AAAA,YACA,QAAQ,IAAI,CAAC,MAAM;AACjB,qBACE,EAAE,OACF,GAAG,QAAQ,SAAS,OAAO,KAAK,EAAE,MAAM,OAAO,EAAE,SAAS,WAAW,CAAC;AAAA,YAE1E,CAAC;AAAA,UACH;AACA,gBAAM,YAAYC,MAAK,WAAW,OAAO,KAAK,IAC1CC,eAAcD,MAAK,SAAS,WAAW,MAAM,OAAO,KAAK,CAAC,IAC1D,OAAO;AACX,gBAAM,WAAW,IAAI,IAAI,WAAW,WAAW;AAC/C,oBAAU,KAAK,SAAS,WAAW,SAAS,MAAM;AAClD,cAAI;AAAA,YACF,UAAU,IAAI,CAAC,MAAM,UAAU,KAAK,UAAU,CAAC,CAAC,GAAG,EAAE,KAAK,IAAI;AAAA,UAChE;AAAA,QACF,WAAW,IAAI,aAAa,UAAU;AACpC,0BAAgB,GAAG;AACnB,cAAI;AAAA,YACF,OAAO;AAAA,cACL,IAAI,aAAa,IAAI,MAAM,KAAK;AAAA,cAChC;AAAA,YACF,EAAE,SAAS,OAAO;AAAA,UACpB;AAAA,QACF,WAAW,IAAI,aAAa,WAAW;AACrC,0BAAgB,GAAG;AACnB,cAAI,OAAO,OAAO,YAAY;AAC5B,gBAAI;AAAA,cACF,KAAK,YAAY,kBAAkB,KAAK,UAAU,aAAa,CAAC;AAAA,YAClE;AAAA,UACF,OAAO;AACL,gBAAI,IAAI,EAAE;AAAA,UACZ;AAAA,QACF,OAAO;AACL,eAAK;AAAA,QACP;AAAA,MACF,CAAC;AAED,UAAI,OAAO,OAAO,MAAM;AACtB,cAAM,OAAO,WAAW,WAAW,UAAU;AAC7C,cAAM,gBAAgB,iDAAiD,IAAI;AAC3E,YAAI,eAAe;AACnB,YAAI,MAAM,UAAU,aAAa,GAAG;AAClC,0BAAgB,MAAME,IAAG,SAAS,aAAa,GAAG,SAAS,OAAO;AAAA,QACpE,OAAO;AACL,gBAAMA,IAAG,MAAMF,MAAK,QAAQ,aAAa,CAAC,EAAE,MAAM,MAAM;AAAA,UAAC,CAAC;AAAA,QAC5D;AACA,cAAM,aAAa,MAAM;AAAA,UACvB;AAAA,UACA,oBAAI,IAAI;AAAA,UACR;AAAA,QACF;AACA,cAAM,aAAa,QAAQ,IAAI,YAAY,eAAe;AAC1D,YAAI,CAAC,YAAY,KAAK,gBAAgB,cAAc,YAAY;AAC9D,sBAAY,UAAU;AACtB,qBAAW,MAAM;AACf,oBAAQ,IAAI,oDAAoD;AAAA,UAClE,CAAC;AAAA,QACH;AACA,cAAME,IAAG,UAAU,eAAe,UAAU,EAAE,MAAM,MAAM;AAAA,QAAC,CAAC;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AACF;;;AE5KO,IAAM,qBAAqB,MAAc;AAC9C,SAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,gBAAgB,QAAQ;AACtB,aAAO,YAAY,IAAI,OAAO,KAAK,KAAK,SAAS;AAC/C,cAAM,MAAM,IAAI,OAAO;AACvB,YAAI,CAAC,KAAK,aAAa,EAAE,SAAS,GAAG,GAAG;AACtC,cAAI,UAAU,gBAAgB,WAAW;AACzC,cAAI,UAAU,iBAAiB,UAAU;AACzC,cAAI,UAAU,+BAA+B,GAAG;AAChD,iBAAO,IAAI,IAAI,SAAS,qBAAqB,eAAe,CAAC;AAAA,QAC/D;AACA,aAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;ACRA,IAAM,WAAW;AAAA,EACf;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAO,kBAAQ;;;AC9Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQO,IAAM,WAAW,CACtB,gBAAgB,IAChB,WAAW,OACiB;AAC5B,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,cAAc,IAAI,cAAc,OAAO;AACrD,YAAM,IAAI,YAAY;AACtB,UAAI,GAAG;AACL,eAAO,gCAAgC,IAAI,IAAI,OAAO,IAAI,CAAC;AAAA,MAC7D,OAAO;AACL,eAAO,gCAAgC,IAAI,IAAI,OAAO;AAAA,MACxD;AAAA,IACF;AAAA,EACF;AACF;AAQO,IAAM,iBAAiB,CAC5B,gBAAgB,IAChB,WAAW,OACiB;AAC5B,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,cAAc,IAAI,cAAc,OAAO;AACrD,YAAM,IAAI,YAAY;AACtB,UAAI,GAAG;AACL,eAAO,mCAAmC,IAAI,IAAI,OAAO,IAAI,CAAC;AAAA,MAChE,OAAO;AACL,eAAO,mCAAmC,IAAI,IAAI,OAAO;AAAA,MAC3D;AAAA,IACF;AAAA,EACF;AACF;AAOO,IAAM,QAAQ,CACnB,gBAAgB,IAChB,WAAW,OACiB;AAC5B,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,cAAc,IAAI,cAAc,OAAO;AACrD,YAAM,IAAI,YAAY;AACtB,UAAI,GAAG;AACL,eAAO,qBAAqB,IAAI,IAAI,OAAO,IAAI,CAAC;AAAA,MAClD,OAAO;AACL,eAAO,qBAAqB,IAAI,IAAI,OAAO;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AACF;AAOO,IAAM,YAAY,CACvB,gBAAgB,IAChB,WAAW,OACiB;AAC5B,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,cAAc,IAAI,cAAc,OAAO;AACrD,YAAM,IAAI,YAAY;AACtB,aAAO,qDAAqD,IAAI,IAAI,OAAO,IAAI,CAAC;AAAA,IAClF;AAAA,EACF;AACF;AAMO,IAAM,UAAU,CACrB,gBAAgB,IAChB,WAAW,OACiB;AAC5B,UAAQ;AAAA,IACN;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,cAAc,IAAI,cAAc,OAAO;AACrD,YAAM,IAAI,YAAY;AACtB,aAAO,mCAAmC,IAAI,IAAI,OAAO,IAAI,CAAC;AAAA,IAChE;AAAA,EACF;AACF;AAOO,IAAM,UAAU,CACrB,gBAAgB,IAChB,WAAW,OACiB;AAC5B,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,cAAc,IAAI,cAAc,OAAO;AACrD,YAAM,IAAI,YAAY;AACtB,aAAO,2BAA2B,IAAI,IAAI,OAAO,IAAI,CAAC;AAAA,IACxD;AAAA,EACF;AACF;AAMO,IAAM,aAAa,CACxB,gBAAgB,IAChB,WAAW,OACiB;AAC5B,UAAQ;AAAA,IACN;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,cAAc,IAAI,cAAc,OAAO;AACrD,YAAM,IAAI,YAAY;AACtB,aAAO,mCAAmC,IAAI,IAAI,OAAO,IAAI,CAAC;AAAA,IAChE;AAAA,EACF;AACF;AAMO,IAAM,QAAQ,CACnB,gBAAgB,IAChB,WAAW,OACiB;AAC5B,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,cAAc,IAAI,cAAc,OAAO;AACrD,YAAM,IAAI,YAAY;AACtB,aAAO,0CAA0C,IAAI,IAAI,OAAO,IAAI,CAAC;AAAA,IACvE;AAAA,EACF;AACF;AAOO,IAAM,QAAQ,CACnB,gBAAgB,IAChB,WAAW,OACiB;AAC5B,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,cAAc,IAAI,cAAc,OAAO;AACrD,YAAM,IAAI,YAAY;AACtB,aAAO,2BAA2B,IAAI,IAAI,OAAO,IAAI,CAAC;AAAA,IACxD;AAAA,EACF;AACF;AAMO,IAAM,WAAW,CACtB,gBAAgB,IAChB,WAAW,OACiB;AAC5B,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,cAAc,IAAI,cAAc,OAAO;AACrD,YAAM,IAAI,YAAY;AACtB,aAAO,4BAA4B,IAAI,IAAI,OAAO,IAAI,CAAC;AAAA,IACzD;AAAA,EACF;AACF;AAMO,IAAM,WAAW,CACtB,gBAAgB,IAChB,WAAW,OACiB;AAC5B,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,cAAc,IAAI,cAAc,OAAO;AACrD,YAAM,IAAI,YAAY;AACtB,aAAO,iCAAiC,IAAI,IAAI,OAAO,IAAI,CAAC;AAAA,IAC9D;AAAA,EACF;AACF;AAMO,IAAM,YAAY,CACvB,gBAAgB,IAChB,WAAW,OACiB;AAC5B,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,cAAc,IAAI,cAAc,OAAO;AACrD,YAAM,IAAI,YAAY;AACtB,UAAI,GAAG;AACL,eAAO,kCAAkC,IAAI,IAAI,OAAO,UAAU,CAAC;AAAA,MACrE,OAAO;AACL,eAAO,kCAAkC,IAAI,IAAI,OAAO;AAAA,MAC1D;AAAA,IACF;AAAA,EACF;AACF;;;AC7NO,IAAM,iBAAiB,OAC5B,iBACkC;AAClC,MAAI,aAAa,QAAQ;AACvB,eAAW,MAAM;AACf,cAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAMC,SAAQ,aAAa,SAAS,CAAC;AAErC,QAAM,EAAE,mBAAmB,CAAC,EAAE,IAAIA;AAClC,QAAM,oBAQF,CAAC;AAEL,aAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,gBAAgB,GAAG;AACrD,QAAI,OAAO,KAAK,UAAU;AACxB,wBAAkB,CAAC,IAAI;AAAA,QACrB,cAAc,MAAM;AAAA,QACpB,aAAa,MAAM;AAAA,MACrB;AAAA,IACF,WAAW,OAAO,KAAK,YAAY;AACjC,wBAAkB,CAAC,IAAI;AAAA,QACrB,cAAc,MAAM;AAAA,QACpB,aAAa;AAAA,MACf;AAAA,IACF,WAAW,aAAa,OAAO;AAC7B,UAAI;AACJ,UAAI,gBAA2B,MAAM;AACrC,YAAM,CAAC,cAAc,WAAW,IAAI;AACpC,UAAI,OAAO,gBAAgB,UAAU;AACnC,wBAAgB,CAAC,QAAQ,gBAAgB,IAAI;AAAA,MAC/C,OAAO;AACL,wBAAgB,CAAC,QACf,aAAa,IAAI,SAAS,IAAI,MAAM,IAAI,YAAY,IAAI,WAAW;AAAA,MACvE;AACA,UAAI,OAAO,eAAe,UAAU;AAClC,uBAAe,MAAM;AAAA,MACvB,OAAO;AACL,uBAAe,CAAC,QACd,YAAY,IAAI,SAAS,IAAI,MAAM,IAAI,YAAY,IAAI,WAAW;AAAA,MACtE;AACA,wBAAkB,CAAC,IAAI;AAAA,QACrB,cAAc;AAAA,QACd,aAAa;AAAA,MACf;AAAA,IACF,OAAO;AACL,YAAM,EAAE,aAAa,QAAQ,YAAY,aAAa,IAAI;AAC1D,UAAI;AACJ,UAAI,gBAA2B,MAAM;AACrC,UAAI,cAEiD;AACrD,UAAI,OAAO,eAAe,UAAU;AAClC,uBAAe,MAAM;AAAA,MACvB,OAAO;AACL,uBAAe;AAAA,MACjB;AACA,UAAI,OAAO,gBAAgB,UAAU;AACnC,wBAAgB,MAAM;AAAA,MACxB,WAAW,OAAO,gBAAgB,YAAY;AAC5C,wBAAgB;AAAA,MAClB;AACA,UAAI,OAAO,cAAc,YAAY;AACnC,sBAAc;AAAA,MAChB,WAAW,OAAO,cAAc,UAAU;AACxC,sBAAc,MAAM;AAAA,MACtB;AACA,wBAAkB,CAAC,IAAI;AAAA,QACrB,cAAc;AAAA,QACd,aAAa;AAAA,QACb;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAEA,QAAM,SAAS,aAAa,UAAU,CAAC;AAEvC,QAAM,EAAE,OAAO,IAAI;AACnB,MAAI,UAAoC,CAAC,MAAM,YAAY;AAC3D,MAAI,OAAO,UAAU,YAAY;AAC/B,cAAU;AAAA,EACZ,WAAW,OAAO,UAAU,UAAU;AACpC,cAAU,MAAM;AAAA,EAClB,WAAW,WAAW,OAAO;AAC3B,cAAU,CAACC,UAAiBA;AAAA,EAC9B;AACA,QAAM,mBAAmBD,QAAO,mBAAmB,CAAC;AACpD,QAAM,kBAA0D,CAAC;AACjE,MAAI,4BAA4B,OAAO;AACrC,qBAAiB,QAAQ,CAAC,MAAM,gBAAgB,KAAK,CAAC,CAAC;AAAA,EACzD,OAAO;AACL,WAAO,QAAQ,gBAAgB,EAAE,QAAQ,CAAC,MAAM,gBAAgB,KAAK,CAAC,CAAC;AAAA,EACzE;AAEA,QAAM,aAAa,MAAM,cAAc;AAEvC,QAAM,EAAE,QAAQ,CAAC,GAAG,SAAS,CAAC,EAAE,IAAI,aAAa,cAAc,CAAC;AAChE,MAAI;AAAA,IACF,OAAO,CAAC;AAAA,IACR,cAAc,CAAC;AAAA,IACf,iBAAiB,eAAe,CAAC;AAAA,IACjC,QAAQ,CAAC;AAAA,IACT,UAAU,CAAC;AAAA,IACX,UAAU,CAAC;AAAA,IACX,cAAc,CAAC;AAAA,IACf,SAAAE,WAAU,CAAC;AAAA,IACX,UAAU,CAAC;AAAA,IACX,aAAa,CAAC;AAAA,EAChB,IAAI,aAAa,cAAc,CAAC;AAChC,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,EAAE,IAAI,KAAK;AAAA,EACpB,WAAW,EAAE,MAAM,OAAO;AACxB,WAAO,EAAE,GAAI,WAAW,OAAO,EAAE,IAAI,WAAW,KAAK,IAAI,CAAC,GAAI,GAAG,KAAK;AAAA,EACxE;AACA,MAAI,OAAO,eAAe,UAAU;AAClC,kBAAc;AAAA,MACZ,IAAI;AAAA,IACN;AAAA,EACF,WAAW,EAAE,MAAM,gBAAgB,WAAW,aAAa;AACzD,kBAAc,EAAE,IAAI,WAAW,aAAa,GAAG,YAAY;AAAA,EAC7D;AACA,MAAI,EAAE,wBAAwB,QAAQ;AACpC,mBAAe,CAAC,YAAY;AAAA,EAC9B;AACA,MAAI,EAAE,iBAAiB,QAAQ;AAC7B,YAAQ,CAAC,KAAK;AAAA,EAChB;AACA,MAAI,EAAE,mBAAmB,QAAQ;AAC/B,cAAU,CAAC,OAAO;AAAA,EACpB;AACA,MAAI,EAAE,mBAAmB,QAAQ;AAC/B,cAAU,CAAC,OAAO;AAAA,EACpB;AACA,MAAI,EAAE,uBAAuB,QAAQ;AACnC,kBAAc,CAAC,WAAW;AAAA,EAC5B;AACA,MAAI,EAAEA,oBAAmB,QAAQ;AAC/B,IAAAA,WAAU,CAACA,QAAO;AAAA,EACpB;AACA,MAAI,EAAE,mBAAmB,QAAQ;AAC/B,cAAU,CAAC,OAAO;AAAA,EACpB;AACA,MAAI,EAAE,sBAAsB,QAAQ;AAClC,iBAAa,CAAC,UAAU;AAAA,EAC1B;AAEA,QAAM,WAAW,oBAAI,IAAY;AACjC,MAAI,OAAO,SAAS,UAAU;AAC5B,aAAS,IAAI,KAAK;AAAA,EACpB,WAAW,iBAAiB,OAAO;AACjC,UAAM,QAAQ,CAAC,MAAM,SAAS,IAAI,CAAC,CAAC;AAAA,EACtC;AAEA,QAAM,QAAiC,CAAC;AACxC,GAAC,kBAAkB,QAAQ,SAAS,OAAO,QAAQ,MAAM,GAAG;AAAA,IAC1D,CAAC,CAAC,GAAG,CAAC,MAAM;AACV,YAAM,KAAK,CAAC,GAAG,GAAI,aAAa,QAAQ,IAAI,CAAC,CAAC,CAAE,CAAC;AAAA,IACnD;AAAA,EACF;AAEA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,WAAW;AAAA,IACrB,SAAS,WAAW;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,WAAW;AAAA,IACxB,cAAc,WAAW;AAAA,IACzB,WAAW,WAAW;AAAA,IACtB;AAAA,IACA,UAAU,WAAW;AAAA,IACrB;AAAA,IACA,SAAS,WAAW;AAAA,IACpB,WAAW,CAAC;AAAA,IACZ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACX,IAAI,aAAa,cAAc,CAAC;AAEhC,QAAM,EAAE,WAAW,WAAW,OAAO,WAAW,IAAIF;AACpD,MAAI,EAAE,aAAa,IAAIA;AACvB,MAAI,OAAO,gBAAgB,UAAU;AACnC,UAAM,IAAI;AACV,mBAAe,MAAM;AAAA,EACvB,WAAW,iBAAiB,MAAM;AAChC,mBAAe,MAAM,SAAS,QAAQ,eAAe,UAAU;AAAA,EACjE,WAAW,iBAAiB,OAAO;AACjC,mBAAe;AAAA,EACjB;AAEA,QAAM,aAAa;AAEnB,QAAM,iBACJA,OAAM,mBACL,MAAM;AACL,WAAO,CAAC,MAAc;AAEpB,UAAI,OAAO,eAAe,YAAY;AAEpC,oBAAY,CAAC;AACb;AAAA,MACF;AACA,YAAM,IAAI,SAAS,cAAc,OAAO;AACxC,QAAE,cAAc;AAChB,eAAS,KAAK,OAAO,CAAC;AAAA,IACxB;AAAA,EACF;AAEF,QAAM,SAA+B;AAAA,IACnC,YAAY;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,eAAe;AAAA,MACf;AAAA,MACA;AAAA,MACA,iBAAiB,aAAa,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;AAAA,MAClD,OAAO,MAAM,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;AAAA,MACjC,SAAS,QAAQ,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;AAAA,MACrC,SAAS,QAAQ,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;AAAA,MACrC;AAAA,MACA,SAAAE;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,OAAO;AAAA,MACP;AAAA,MACA,KAAK,MAAO,eAAe,QAAQ,MAAM,CAAC,GAAG,IAAK,CAAC;AAAA,MACnD;AAAA,MACA,YAAY,WAAW,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,IACrD;AAAA,IACA,aAAa,aAAa,eAAe;AAAA,IACzC,OAAO,aAAa;AAAA,IACpB,OAAO,aAAa,UAAU,QAAQ,IAAK,aAAa,SAAS;AAAA,IACjE,UAAU,aAAa,aAAa,CAAC,aAAa,SAAS;AAAA,IAC3D,QAAQ;AAAA,MACN,YAAY,OAAO,cAAc;AAAA,MACjC,MACE,OAAO,SACN,QAAQ,YAAY,WAAW,QAAQ,YAAY;AAAA,MACtD,QAAQ;AAAA,IACV;AAAA,IACA,OAAO;AAAA,MACL;AAAA,MACA,cAAc,aAAa,MAAM,WAAW,QAAQ,IAAI;AAAA,MACxD,WAAWF,OAAM,aAAa;AAAA,MAC9B;AAAA,MACA,kBAAkB;AAAA,IACpB;AAAA,IACA,oBAAoB,CAAC;AAAA,IACrB,iBAAiB,CAAC;AAAA,IAClB,oBAAoB,CAAC;AAAA,IACrB,gBAAgB,CAAC;AAAA,IACjB,UAAUA,OAAM,YAAY,SAAS,EAAE,CAAC;AAAA,IACxC,gBAAgB,OAAO,QAAQ;AAC7B,YAAM,WAAW,MAAM,eAAe,GAAG;AACzC,UAAI,OAAO,YAAY,UAAU;AAC/B,eAAO;AAAA,MACT;AACA,aAAO,SAAS,kBAAkB,UAAU,GAAG,GAAG,IAAI;AAAA,IACxD;AAAA,EACF;AAEA,SAAO;AACT;;;ACrTA,IAAO,gBAAQ,CAAC,iBAAyC;AACvD,MAAI;AACJ,SAAO,gBAAS;AAAA,IAAI,CAAC,MACnB,EAAE,YAAY;AACZ,aAAO,UAAU,eAAe,YAAY,EAAE,KAAK,CAAC,MAAO,SAAS,CAAE;AAAA,IACxE,CAAC;AAAA,EACH;AACF;AAOA,IAAM,iBAAiB;AAAA,EACrB,MAAM;AAAA,EACN,SAAS,CAAC,MAAM,GAAG,eAAe,gBAAgB,cAAc;AAClE;AAEO,IAAM,OAAO;AAAA,EAClB;AAAA,EACA;AACF;", "names": ["require", "description", "tag", "maxLen", "path", "base", "fs", "fs", "s", "acornWalk", "chunk", "_", "normalizePath", "fs", "path", "fs", "path", "normalizePath", "normalizePath", "normalizePath", "path", "normalizePath", "path", "normalizePath", "fs", "path", "normalizePath", "path", "path", "resolve", "path", "normalizePath", "fs", "build", "name", "require"]}