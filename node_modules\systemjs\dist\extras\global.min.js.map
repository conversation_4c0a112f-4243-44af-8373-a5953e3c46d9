{"version": 3, "file": "##.min.js", "names": ["global", "shouldSkipProperty", "p", "hasOwnProperty", "isNaN", "length", "isIE11", "window", "parent", "firstGlobalProp", "secondGlobalProp", "lastGlobalProp", "systemJSPrototype", "System", "constructor", "prototype", "impt", "import", "id", "parentUrl", "meta", "undefined", "noteGlobalProps", "call", "this", "emptyInstantiation", "getRegister", "lastRegister", "globalExport", "globalProp", "useFirstGlobalProp", "foundLastProp", "result", "cnt", "getGlobalProp", "e", "_export", "execute", "default", "__use<PERSON>efault", "navigator", "userAgent", "indexOf", "self"], "sources": ["global.js"], "sourcesContent": ["(function () {\n\n  /*\r\n   * SystemJS global script loading support\r\n   * Extra for the s.js build only\r\n   * (Included by default in system.js build)\r\n   */\r\n  (function (global) {\r\n    var systemJSPrototype = global.System.constructor.prototype;\r\n\r\n    // safari unpredictably lists some new globals first or second in object order\r\n    var firstGlobalProp, secondGlobalProp, lastGlobalProp;\r\n    function getGlobalProp (useFirstGlobalProp) {\r\n      var cnt = 0;\r\n      var foundLastProp, result;\r\n      for (var p in global) {\r\n        // do not check frames cause it could be removed during import\r\n        if (shouldSkipProperty(p))\r\n          continue;\r\n        if (cnt === 0 && p !== firstGlobalProp || cnt === 1 && p !== secondGlobalProp)\r\n          return p;\r\n        if (foundLastProp) {\r\n          lastGlobalProp = p;\r\n          result = useFirstGlobalProp && result || p;\r\n        }\r\n        else {\r\n          foundLastProp = p === lastGlobalProp;\r\n        }\r\n        cnt++;\r\n      }\r\n      return result;\r\n    }\r\n\r\n    function noteGlobalProps () {\r\n      // alternatively Object.keys(global).pop()\r\n      // but this may be faster (pending benchmarks)\r\n      firstGlobalProp = secondGlobalProp = undefined;\r\n      for (var p in global) {\r\n        // do not check frames cause it could be removed during import\r\n        if (shouldSkipProperty(p))\r\n          continue;\r\n        if (!firstGlobalProp)\r\n          firstGlobalProp = p;\r\n        else if (!secondGlobalProp)\r\n          secondGlobalProp = p;\r\n        lastGlobalProp = p;\r\n      }\r\n      return lastGlobalProp;\r\n    }\r\n\r\n    var impt = systemJSPrototype.import;\r\n    systemJSPrototype.import = function (id, parentUrl, meta) {\r\n      noteGlobalProps();\r\n      return impt.call(this, id, parentUrl, meta);\r\n    };\r\n\r\n    var emptyInstantiation = [[], function () { return {} }];\r\n\r\n    var getRegister = systemJSPrototype.getRegister;\r\n    systemJSPrototype.getRegister = function () {\r\n      var lastRegister = getRegister.call(this);\r\n      if (lastRegister)\r\n        return lastRegister;\r\n\r\n      // no registration -> attempt a global detection as difference from snapshot\r\n      // when multiple globals, we take the global value to be the last defined new global object property\r\n      // for performance, this will not support multi-version / global collisions as previous SystemJS versions did\r\n      // note in Edge, deleting and re-adding a global does not change its ordering\r\n      var globalProp = getGlobalProp(this.firstGlobalProp);\r\n      if (!globalProp)\r\n        return emptyInstantiation;\r\n\r\n      var globalExport;\r\n      try {\r\n        globalExport = global[globalProp];\r\n      }\r\n      catch (e) {\r\n        return emptyInstantiation;\r\n      }\r\n\r\n      return [[], function (_export) {\r\n        return {\r\n          execute: function () {\r\n            _export(globalExport);\r\n            _export({ default: globalExport, __useDefault: true });\r\n          }\r\n        };\r\n      }];\r\n    };\r\n\r\n    var isIE11 = typeof navigator !== 'undefined' && navigator.userAgent.indexOf('Trident') !== -1;\r\n\r\n    function shouldSkipProperty(p) {\r\n      return !global.hasOwnProperty(p)\r\n        || !isNaN(p) && p < global.length\r\n        || isIE11 && global[p] && typeof window !== 'undefined' && global[p].parent === window;\r\n    }\r\n  })(typeof self !== 'undefined' ? self : global);\n\n})();\n"], "mappings": "CAOE,SAAWA,GAqFT,SAASC,EAAmBC,GAC1B,OAAQF,EAAOG,eAAeD,KACxBE,MAAMF,IAAMA,EAAIF,EAAOK,QACxBC,GAAUN,EAAOE,IAAwB,oBAAXK,QAA0BP,EAAOE,GAAGM,SAAWD,MACpF,CAxFA,IAGIE,EAAiBC,EAAkBC,EAHnCC,EAAoBZ,EAAOa,OAAOC,YAAYC,UA0C9CC,EAAOJ,EAAkBK,OAC7BL,EAAkBK,OAAS,SAAUC,EAAIC,EAAWC,GAElD,OApBF,WAIE,IAAK,IAAIlB,KADTO,EAAkBC,OAAmBW,EACvBrB,EAERC,EAAmBC,KAElBO,EAEKC,IACRA,EAAmBR,GAFnBO,EAAkBP,EAGpBS,EAAiBT,EAGrB,CAIEoB,GACON,EAAKO,KAAKC,KAAMN,EAAIC,EAAWC,EACxC,EAEA,IAAIK,EAAqB,CAAC,GAAI,WAAc,MAAO,CAAC,CAAE,GAElDC,EAAcd,EAAkBc,YACpCd,EAAkBc,YAAc,WAC9B,IAAIC,EAAeD,EAAYH,KAAKC,MACpC,GAAIG,EACF,OAAOA,EAMT,IAIIC,EAJAC,EAxDN,SAAwBC,GACtB,IACIC,EAAeC,EADfC,EAAM,EAEV,IAAK,IAAI/B,KAAKF,EAEZ,IAAIC,EAAmBC,GAAvB,CAEA,GAAY,IAAR+B,GAAa/B,IAAMO,GAA2B,IAARwB,GAAa/B,IAAMQ,EAC3D,OAAOR,EACL6B,GACFpB,EAAiBT,EACjB8B,EAASF,GAAsBE,GAAU9B,GAGzC6B,EAAgB7B,IAAMS,EAExBsB,GAVU,CAYZ,OAAOD,CACT,CAqCmBE,CAAcV,KAAKf,iBACpC,IAAKoB,EACH,OAAOJ,EAGT,IACEG,EAAe5B,EAAO6B,EAIxB,CAFA,MAAOM,GACL,OAAOV,CACT,CAEA,MAAO,CAAC,GAAI,SAAUW,GACpB,MAAO,CACLC,QAAS,WACPD,EAAQR,GACRQ,EAAQ,CAAEE,QAASV,EAAcW,cAAc,GACjD,EAEJ,EACF,EAEA,IAAIjC,EAA8B,oBAAdkC,YAAyE,IAA5CA,UAAUC,UAAUC,QAAQ,UAO9E,CA1FD,CA0FmB,oBAATC,KAAuBA,KAAO3C"}